// 注意：这个演示需要在支持 Vue 3 的环境中运行
// 如果在浏览器中直接运行，需要通过 CDN 引入 Vue
// 这里假设 Vue 已经通过 CDN 或其他方式全局可用

// 导入 OFD Viewer 组件（需要构建系统支持）
// import OfdViewer from '../../src/ofdViewer/ofdViewer.vue'

// 由于这是一个演示页面，我们使用全局的 Vue
const { createApp, ref, onMounted } = Vue

const App = {
  components: {
    // OfdViewer - 在实际使用中需要正确导入
    // 这里我们创建一个简化的演示组件
    'ofd-viewer': {
      template: `
        <div class="ofd-viewer-placeholder">
          <div v-if="!src" class="placeholder-content">
            <h3>OFD Viewer 组件演示</h3>
            <p>请上传 OFD 文件或点击"加载示例文件"按钮</p>
            <div class="placeholder-features">
              <h4>组件特性：</h4>
              <ul>
                <li>✅ 支持 OFD 文件解析和渲染</li>
                <li>✅ 内置工具栏（缩放、导航）</li>
                <li>✅ 响应式设计</li>
                <li>✅ 事件回调支持</li>
                <li>✅ 可自定义样式</li>
              </ul>
            </div>
          </div>
          <div v-else class="ofd-content">
            <div class="mock-toolbar">
              <button @click="$emit('page-change', Math.max(1, currentPage - 1))">上一页</button>
              <span>{{ currentPage }} / {{ totalPages }}</span>
              <button @click="$emit('page-change', Math.min(totalPages, currentPage + 1))">下一页</button>
              <button @click="$emit('scale-change', scale * 1.2)">放大</button>
              <button @click="$emit('scale-change', scale * 0.8)">缩小</button>
            </div>
            <div class="mock-document">
              <p>这里将显示 OFD 文档内容</p>
              <p>文件: {{ fileName }}</p>
              <p>当前页: {{ currentPage }} / {{ totalPages }}</p>
              <p>缩放: {{ Math.round(scale * 100) }}%</p>
            </div>
          </div>
        </div>
      `,
      props: {
        src: [String, File, ArrayBuffer],
        showToolbar: { type: Boolean, default: true },
        autoLoad: { type: Boolean, default: true },
        scale: { type: Number, default: 1 }
      },
      emits: ['loaded', 'error', 'page-change', 'scale-change', 'signature-click'],
      data() {
        return {
          currentPage: 1,
          totalPages: 5,
          fileName: '',
          isLoaded: false
        }
      },
      watch: {
        src: {
          handler(newSrc) {
            if (newSrc) {
              this.loadFile(newSrc)
            }
          },
          immediate: true
        }
      },
      methods: {
        loadFile(file) {
          // 模拟加载过程
          setTimeout(() => {
            if (file instanceof File) {
              this.fileName = file.name
            } else {
              this.fileName = 'sample.ofd'
            }
            this.isLoaded = true
            this.$emit('loaded', { pages: this.totalPages })
          }, 1000)
        },
        getCurrentPage() { return this.currentPage },
        getTotalPages() { return this.totalPages },
        getScale() { return this.scale }
      },
      style: `
        .ofd-viewer-placeholder {
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          background: #f8f9fa;
          border-radius: 4px;
        }
        .placeholder-content {
          text-align: center;
          padding: 40px;
          max-width: 500px;
        }
        .placeholder-features {
          margin-top: 20px;
          text-align: left;
        }
        .placeholder-features ul {
          list-style: none;
          padding: 0;
        }
        .placeholder-features li {
          padding: 5px 0;
        }
        .ofd-content {
          height: 100%;
          display: flex;
          flex-direction: column;
        }
        .mock-toolbar {
          padding: 10px;
          background: #fff;
          border-bottom: 1px solid #ddd;
          display: flex;
          gap: 10px;
          align-items: center;
        }
        .mock-document {
          flex: 1;
          padding: 20px;
          background: white;
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
        }
      `
    }
  },
  setup() {
    // 响应式数据
    const ofdViewerRef = ref(null)
    const currentFile = ref(null)
    const isLoading = ref(false)
    const errorMessage = ref('')
    
    const viewerStatus = ref({
      fileName: '',
      totalPages: 0,
      currentPage: 1,
      scale: 1
    })

    // 处理文件上传
    const handleFileUpload = (event) => {
      const file = event.target.files[0]
      if (file) {
        if (!file.name.toLowerCase().endsWith('.ofd')) {
          errorMessage.value = '请选择 .ofd 格式的文件'
          return
        }
        
        errorMessage.value = ''
        viewerStatus.value.fileName = file.name
        currentFile.value = file
        isLoading.value = true
      }
    }

    // 加载示例文件
    const loadSampleFile = async () => {
      try {
        errorMessage.value = ''
        isLoading.value = true
        
        // 这里可以加载一个示例 OFD 文件
        // 由于没有示例文件，我们显示一个提示
        errorMessage.value = '请上传一个 OFD 文件进行演示'
        isLoading.value = false
        
        // 如果有示例文件，可以这样加载：
        // const response = await fetch('./sample.ofd')
        // const arrayBuffer = await response.arrayBuffer()
        // currentFile.value = arrayBuffer
        // viewerStatus.value.fileName = 'sample.ofd'
        
      } catch (error) {
        console.error('加载示例文件失败:', error)
        errorMessage.value = '加载示例文件失败: ' + error.message
        isLoading.value = false
      }
    }

    // 文档加载成功回调
    const onDocumentLoaded = (document) => {
      console.log('文档加载成功:', document)
      isLoading.value = false
      errorMessage.value = ''
      
      if (ofdViewerRef.value) {
        viewerStatus.value.totalPages = ofdViewerRef.value.getTotalPages()
        viewerStatus.value.currentPage = ofdViewerRef.value.getCurrentPage()
        viewerStatus.value.scale = ofdViewerRef.value.getScale()
      }
    }

    // 文档加载错误回调
    const onDocumentError = (error) => {
      console.error('文档加载失败:', error)
      isLoading.value = false
      errorMessage.value = '文档加载失败: ' + error.message
    }

    // 页面变化回调
    const onPageChange = (page) => {
      console.log('页面变化:', page)
      viewerStatus.value.currentPage = page
    }

    // 缩放变化回调
    const onScaleChange = (scale) => {
      console.log('缩放变化:', scale)
      viewerStatus.value.scale = scale
    }

    // 签名点击回调
    const onSignatureClick = (data) => {
      console.log('签名点击:', data)
      alert('检测到数字签名点击事件，详情请查看控制台')
    }

    // 组件挂载后的操作
    onMounted(() => {
      console.log('OFD Viewer Vue 演示页面已加载')
    })

    return {
      // 引用
      ofdViewerRef,
      
      // 数据
      currentFile,
      isLoading,
      errorMessage,
      viewerStatus,
      
      // 方法
      handleFileUpload,
      loadSampleFile,
      onDocumentLoaded,
      onDocumentError,
      onPageChange,
      onScaleChange,
      onSignatureClick
    }
  }
}

// 创建并挂载应用
createVueApp(App).mount('#app')
