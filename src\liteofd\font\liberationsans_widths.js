/* Copyright 2021 Mozilla Foundation
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

// Widths of glyphs in LiberationSans-Bold.ttf.
const LiberationSansBoldWidths = [
  365, 0, 333, 278, 333, 474, 556, 556, 889, 722, 238, 333, 333, 389, 584, 278,
  333, 278, 278, 556, 556, 556, 556, 556, 556, 556, 556, 556, 556, 333, 333,
  584, 584, 584, 611, 975, 722, 722, 722, 722, 667, 611, 778, 722, 278, 556,
  722, 611, 833, 722, 778, 667, 778, 722, 667, 611, 722, 667, 944, 667, 667,
  611, 333, 278, 333, 584, 556, 333, 556, 611, 556, 611, 556, 333, 611, 611,
  278, 278, 556, 278, 889, 611, 611, 611, 611, 389, 556, 333, 611, 556, 778,
  556, 556, 500, 389, 280, 389, 584, 333, 556, 556, 556, 556, 280, 556, 333,
  737, 370, 556, 584, 737, 552, 400, 549, 333, 333, 333, 576, 556, 278, 333,
  333, 365, 556, 834, 834, 834, 611, 722, 722, 722, 722, 722, 722, 1000, 722,
  667, 667, 667, 667, 278, 278, 278, 278, 722, 722, 778, 778, 778, 778, 778,
  584, 778, 722, 722, 722, 722, 667, 667, 611, 556, 556, 556, 556, 556, 556,
  889, 556, 556, 556, 556, 556, 278, 278, 278, 278, 611, 611, 611, 611, 611,
  611, 611, 549, 611, 611, 611, 611, 611, 556, 611, 556, 722, 556, 722, 556,
  722, 556, 722, 556, 722, 556, 722, 556, 722, 556, 722, 719, 722, 611, 667,
  556, 667, 556, 667, 556, 667, 556, 667, 556, 778, 611, 778, 611, 778, 611,
  778, 611, 722, 611, 722, 611, 278, 278, 278, 278, 278, 278, 278, 278, 278,
  278, 785, 556, 556, 278, 722, 556, 556, 611, 278, 611, 278, 611, 385, 611,
  479, 611, 278, 722, 611, 722, 611, 722, 611, 708, 723, 611, 778, 611, 778,
  611, 778, 611, 1000, 944, 722, 389, 722, 389, 722, 389, 667, 556, 667, 556,
  667, 556, 667, 556, 611, 333, 611, 479, 611, 333, 722, 611, 722, 611, 722,
  611, 722, 611, 722, 611, 722, 611, 944, 778, 667, 556, 667, 611, 500, 611,
  500, 611, 500, 278, 556, 722, 556, 1000, 889, 778, 611, 667, 556, 611, 333,
  333, 333, 333, 333, 333, 333, 333, 333, 333, 333, 465, 722, 333, 853, 906,
  474, 825, 927, 838, 278, 722, 722, 601, 719, 667, 611, 722, 778, 278, 722,
  667, 833, 722, 644, 778, 722, 667, 600, 611, 667, 821, 667, 809, 802, 278,
  667, 615, 451, 611, 278, 582, 615, 610, 556, 606, 475, 460, 611, 541, 278,
  558, 556, 612, 556, 445, 611, 766, 619, 520, 684, 446, 582, 715, 576, 753,
  845, 278, 582, 611, 582, 845, 667, 669, 885, 567, 711, 667, 278, 276, 556,
  1094, 1062, 875, 610, 722, 622, 719, 722, 719, 722, 567, 712, 667, 904, 626,
  719, 719, 610, 702, 833, 722, 778, 719, 667, 722, 611, 622, 854, 667, 730,
  703, 1005, 1019, 870, 979, 719, 711, 1031, 719, 556, 618, 615, 417, 635, 556,
  709, 497, 615, 615, 500, 635, 740, 604, 611, 604, 611, 556, 490, 556, 875,
  556, 615, 581, 833, 844, 729, 854, 615, 552, 854, 583, 556, 556, 611, 417,
  552, 556, 278, 281, 278, 969, 906, 611, 500, 615, 556, 604, 778, 611, 487,
  447, 944, 778, 944, 778, 944, 778, 667, 556, 333, 333, 556, 1000, 1000, 552,
  278, 278, 278, 278, 500, 500, 500, 556, 556, 350, 1000, 1000, 240, 479, 333,
  333, 604, 333, 167, 396, 556, 556, 1094, 556, 885, 489, 1115, 1000, 768, 600,
  834, 834, 834, 834, 1000, 500, 1000, 500, 1000, 500, 500, 494, 612, 823, 713,
  584, 549, 713, 979, 722, 274, 549, 549, 583, 549, 549, 604, 584, 604, 604,
  708, 625, 708, 708, 708, 708, 708, 708, 708, 708, 708, 708, 708, 708, 708,
  708, 708, 708, 708, 708, 708, 708, 708, 708, 708, 708, 708, 708, 708, 708,
  708, 708, 708, 708, 708, 708, 708, 708, 708, 708, 708, 708, 708, 708, 708,
  708, 708, 729, 604, 604, 354, 354, 1000, 990, 990, 990, 990, 494, 604, 604,
  604, 604, 354, 1021, 1052, 917, 750, 750, 531, 656, 594, 510, 500, 750, 750,
  611, 611, 333, 333, 333, 333, 333, 333, 333, 333, 222, 222, 333, 333, 333,
  333, 333, 333, 333, 333,
];

// Char code of glyphs in LiberationSans-Bold.ttf.
const LiberationSansBoldMapping = [
  -1, -1, -1, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47,
  48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66,
  67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85,
  86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103,
  104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118,
  119, 120, 121, 122, 123, 124, 125, 126, 161, 162, 163, 164, 165, 166, 167,
  168, 169, 170, 171, 172, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183,
  184, 185, 186, 187, 188, 189, 190, 191, 192, 193, 194, 195, 196, 197, 198,
  199, 200, 201, 202, 203, 204, 205, 206, 207, 208, 209, 210, 211, 212, 213,
  214, 215, 216, 217, 218, 219, 220, 221, 222, 223, 224, 225, 226, 227, 228,
  229, 230, 231, 232, 233, 234, 235, 236, 237, 238, 239, 240, 241, 242, 243,
  244, 245, 246, 247, 248, 249, 250, 251, 252, 253, 254, 255, 256, 257, 258,
  259, 260, 261, 262, 263, 264, 265, 266, 267, 268, 269, 270, 271, 272, 273,
  274, 275, 276, 277, 278, 279, 280, 281, 282, 283, 284, 285, 286, 287, 288,
  289, 290, 291, 292, 293, 294, 295, 296, 297, 298, 299, 300, 301, 302, 303,
  304, 305, 306, 307, 308, 309, 310, 311, 312, 313, 314, 315, 316, 317, 318,
  319, 320, 321, 322, 323, 324, 325, 326, 327, 328, 329, 330, 331, 332, 333,
  334, 335, 336, 337, 338, 339, 340, 341, 342, 343, 344, 345, 346, 347, 348,
  349, 350, 351, 352, 353, 354, 355, 356, 357, 358, 359, 360, 361, 362, 363,
  364, 365, 366, 367, 368, 369, 370, 371, 372, 373, 374, 375, 376, 377, 378,
  379, 380, 381, 382, 383, 402, 506, 507, 508, 509, 510, 511, 536, 537, 538,
  539, 710, 711, 713, 728, 729, 730, 731, 732, 733, 900, 901, 902, 903, 904,
  905, 906, 908, 910, 911, 912, 913, 914, 915, 916, 917, 918, 919, 920, 921,
  922, 923, 924, 925, 926, 927, 928, 929, 931, 932, 933, 934, 935, 936, 937,
  938, 939, 940, 941, 942, 943, 944, 945, 946, 947, 948, 949, 950, 951, 952,
  953, 954, 955, 956, 957, 958, 959, 960, 961, 962, 963, 964, 965, 966, 967,
  968, 969, 970, 971, 972, 973, 974, 1024, 1025, 1026, 1027, 1028, 1029, 1030,
  1031, 1032, 1033, 1034, 1035, 1036, 1037, 1038, 1039, 1040, 1041, 1042, 1043,
  1044, 1045, 1046, 1047, 1048, 1049, 1050, 1051, 1052, 1053, 1054, 1055, 1056,
  1057, 1058, 1059, 1060, 1061, 1062, 1063, 1064, 1065, 1066, 1067, 1068, 1069,
  1070, 1071, 1072, 1073, 1074, 1075, 1076, 1077, 1078, 1079, 1080, 1081, 1082,
  1083, 1084, 1085, 1086, 1087, 1088, 1089, 1090, 1091, 1092, 1093, 1094, 1095,
  1096, 1097, 1098, 1099, 1100, 1101, 1102, 1103, 1104, 1105, 1106, 1107, 1108,
  1109, 1110, 1111, 1112, 1113, 1114, 1115, 1116, 1117, 1118, 1119, 1138, 1139,
  1168, 1169, 7808, 7809, 7810, 7811, 7812, 7813, 7922, 7923, 8208, 8209, 8211,
  8212, 8213, 8215, 8216, 8217, 8218, 8219, 8220, 8221, 8222, 8224, 8225, 8226,
  8230, 8240, 8242, 8243, 8249, 8250, 8252, 8254, 8260, 8319, 8355, 8356, 8359,
  8364, 8453, 8467, 8470, 8482, 8486, 8494, 8539, 8540, 8541, 8542, 8592, 8593,
  8594, 8595, 8596, 8597, 8616, 8706, 8710, 8719, 8721, 8722, 8730, 8734, 8735,
  8745, 8747, 8776, 8800, 8801, 8804, 8805, 8962, 8976, 8992, 8993, 9472, 9474,
  9484, 9488, 9492, 9496, 9500, 9508, 9516, 9524, 9532, 9552, 9553, 9554, 9555,
  9556, 9557, 9558, 9559, 9560, 9561, 9562, 9563, 9564, 9565, 9566, 9567, 9568,
  9569, 9570, 9571, 9572, 9573, 9574, 9575, 9576, 9577, 9578, 9579, 9580, 9600,
  9604, 9608, 9612, 9616, 9617, 9618, 9619, 9632, 9633, 9642, 9643, 9644, 9650,
  9658, 9660, 9668, 9674, 9675, 9679, 9688, 9689, 9702, 9786, 9787, 9788, 9792,
  9794, 9824, 9827, 9829, 9830, 9834, 9835, 9836, 61441, 61442, 61445, -1, -1,
  -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,
];

// Widths of glyphs in LiberationSans-BoldItalic.ttf.
const LiberationSansBoldItalicWidths = [
  365, 0, 333, 278, 333, 474, 556, 556, 889, 722, 238, 333, 333, 389, 584, 278,
  333, 278, 278, 556, 556, 556, 556, 556, 556, 556, 556, 556, 556, 333, 333,
  584, 584, 584, 611, 975, 722, 722, 722, 722, 667, 611, 778, 722, 278, 556,
  722, 611, 833, 722, 778, 667, 778, 722, 667, 611, 722, 667, 944, 667, 667,
  611, 333, 278, 333, 584, 556, 333, 556, 611, 556, 611, 556, 333, 611, 611,
  278, 278, 556, 278, 889, 611, 611, 611, 611, 389, 556, 333, 611, 556, 778,
  556, 556, 500, 389, 280, 389, 584, 333, 556, 556, 556, 556, 280, 556, 333,
  737, 370, 556, 584, 737, 552, 400, 549, 333, 333, 333, 576, 556, 278, 333,
  333, 365, 556, 834, 834, 834, 611, 722, 722, 722, 722, 722, 722, 1000, 722,
  667, 667, 667, 667, 278, 278, 278, 278, 722, 722, 778, 778, 778, 778, 778,
  584, 778, 722, 722, 722, 722, 667, 667, 611, 556, 556, 556, 556, 556, 556,
  889, 556, 556, 556, 556, 556, 278, 278, 278, 278, 611, 611, 611, 611, 611,
  611, 611, 549, 611, 611, 611, 611, 611, 556, 611, 556, 722, 556, 722, 556,
  722, 556, 722, 556, 722, 556, 722, 556, 722, 556, 722, 740, 722, 611, 667,
  556, 667, 556, 667, 556, 667, 556, 667, 556, 778, 611, 778, 611, 778, 611,
  778, 611, 722, 611, 722, 611, 278, 278, 278, 278, 278, 278, 278, 278, 278,
  278, 782, 556, 556, 278, 722, 556, 556, 611, 278, 611, 278, 611, 396, 611,
  479, 611, 278, 722, 611, 722, 611, 722, 611, 708, 723, 611, 778, 611, 778,
  611, 778, 611, 1000, 944, 722, 389, 722, 389, 722, 389, 667, 556, 667, 556,
  667, 556, 667, 556, 611, 333, 611, 479, 611, 333, 722, 611, 722, 611, 722,
  611, 722, 611, 722, 611, 722, 611, 944, 778, 667, 556, 667, 611, 500, 611,
  500, 611, 500, 278, 556, 722, 556, 1000, 889, 778, 611, 667, 556, 611, 333,
  333, 333, 333, 333, 333, 333, 333, 333, 333, 333, 333, 722, 333, 854, 906,
  473, 844, 930, 847, 278, 722, 722, 610, 671, 667, 611, 722, 778, 278, 722,
  667, 833, 722, 657, 778, 718, 667, 590, 611, 667, 822, 667, 829, 781, 278,
  667, 620, 479, 611, 278, 591, 620, 621, 556, 610, 479, 492, 611, 558, 278,
  566, 556, 603, 556, 450, 611, 712, 605, 532, 664, 409, 591, 704, 578, 773,
  834, 278, 591, 611, 591, 834, 667, 667, 886, 614, 719, 667, 278, 278, 556,
  1094, 1042, 854, 622, 719, 677, 719, 722, 708, 722, 614, 722, 667, 927, 643,
  719, 719, 615, 687, 833, 722, 778, 719, 667, 722, 611, 677, 781, 667, 729,
  708, 979, 989, 854, 1000, 708, 719, 1042, 729, 556, 619, 604, 534, 618, 556,
  736, 510, 611, 611, 507, 622, 740, 604, 611, 611, 611, 556, 889, 556, 885,
  556, 646, 583, 889, 935, 707, 854, 594, 552, 865, 589, 556, 556, 611, 469,
  563, 556, 278, 278, 278, 969, 906, 611, 507, 619, 556, 611, 778, 611, 575,
  467, 944, 778, 944, 778, 944, 778, 667, 556, 333, 333, 556, 1000, 1000, 552,
  278, 278, 278, 278, 500, 500, 500, 556, 556, 350, 1000, 1000, 240, 479, 333,
  333, 604, 333, 167, 396, 556, 556, 1104, 556, 885, 516, 1146, 1000, 768, 600,
  834, 834, 834, 834, 999, 500, 1000, 500, 1000, 500, 500, 494, 612, 823, 713,
  584, 549, 713, 979, 722, 274, 549, 549, 583, 549, 549, 604, 584, 604, 604,
  708, 625, 708, 708, 708, 708, 708, 708, 708, 708, 708, 708, 708, 708, 708,
  708, 708, 708, 708, 708, 708, 708, 708, 708, 708, 708, 708, 708, 708, 708,
  708, 708, 708, 708, 708, 708, 708, 708, 708, 708, 708, 708, 708, 708, 708,
  708, 708, 729, 604, 604, 354, 354, 1000, 990, 990, 990, 990, 494, 604, 604,
  604, 604, 354, 1021, 1052, 917, 750, 750, 531, 656, 594, 510, 500, 750, 750,
  611, 611, 333, 333, 333, 333, 333, 333, 333, 333, 222, 222, 333, 333, 333,
  333, 333, 333, 333, 333,
];

// Char code of glyphs in LiberationSans-BoldItalic.ttf.
const LiberationSansBoldItalicMapping = [
  -1, -1, -1, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47,
  48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66,
  67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85,
  86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103,
  104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118,
  119, 120, 121, 122, 123, 124, 125, 126, 161, 162, 163, 164, 165, 166, 167,
  168, 169, 170, 171, 172, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183,
  184, 185, 186, 187, 188, 189, 190, 191, 192, 193, 194, 195, 196, 197, 198,
  199, 200, 201, 202, 203, 204, 205, 206, 207, 208, 209, 210, 211, 212, 213,
  214, 215, 216, 217, 218, 219, 220, 221, 222, 223, 224, 225, 226, 227, 228,
  229, 230, 231, 232, 233, 234, 235, 236, 237, 238, 239, 240, 241, 242, 243,
  244, 245, 246, 247, 248, 249, 250, 251, 252, 253, 254, 255, 256, 257, 258,
  259, 260, 261, 262, 263, 264, 265, 266, 267, 268, 269, 270, 271, 272, 273,
  274, 275, 276, 277, 278, 279, 280, 281, 282, 283, 284, 285, 286, 287, 288,
  289, 290, 291, 292, 293, 294, 295, 296, 297, 298, 299, 300, 301, 302, 303,
  304, 305, 306, 307, 308, 309, 310, 311, 312, 313, 314, 315, 316, 317, 318,
  319, 320, 321, 322, 323, 324, 325, 326, 327, 328, 329, 330, 331, 332, 333,
  334, 335, 336, 337, 338, 339, 340, 341, 342, 343, 344, 345, 346, 347, 348,
  349, 350, 351, 352, 353, 354, 355, 356, 357, 358, 359, 360, 361, 362, 363,
  364, 365, 366, 367, 368, 369, 370, 371, 372, 373, 374, 375, 376, 377, 378,
  379, 380, 381, 382, 383, 402, 506, 507, 508, 509, 510, 511, 536, 537, 538,
  539, 710, 711, 713, 728, 729, 730, 731, 732, 733, 900, 901, 902, 903, 904,
  905, 906, 908, 910, 911, 912, 913, 914, 915, 916, 917, 918, 919, 920, 921,
  922, 923, 924, 925, 926, 927, 928, 929, 931, 932, 933, 934, 935, 936, 937,
  938, 939, 940, 941, 942, 943, 944, 945, 946, 947, 948, 949, 950, 951, 952,
  953, 954, 955, 956, 957, 958, 959, 960, 961, 962, 963, 964, 965, 966, 967,
  968, 969, 970, 971, 972, 973, 974, 1024, 1025, 1026, 1027, 1028, 1029, 1030,
  1031, 1032, 1033, 1034, 1035, 1036, 1037, 1038, 1039, 1040, 1041, 1042, 1043,
  1044, 1045, 1046, 1047, 1048, 1049, 1050, 1051, 1052, 1053, 1054, 1055, 1056,
  1057, 1058, 1059, 1060, 1061, 1062, 1063, 1064, 1065, 1066, 1067, 1068, 1069,
  1070, 1071, 1072, 1073, 1074, 1075, 1076, 1077, 1078, 1079, 1080, 1081, 1082,
  1083, 1084, 1085, 1086, 1087, 1088, 1089, 1090, 1091, 1092, 1093, 1094, 1095,
  1096, 1097, 1098, 1099, 1100, 1101, 1102, 1103, 1104, 1105, 1106, 1107, 1108,
  1109, 1110, 1111, 1112, 1113, 1114, 1115, 1116, 1117, 1118, 1119, 1138, 1139,
  1168, 1169, 7808, 7809, 7810, 7811, 7812, 7813, 7922, 7923, 8208, 8209, 8211,
  8212, 8213, 8215, 8216, 8217, 8218, 8219, 8220, 8221, 8222, 8224, 8225, 8226,
  8230, 8240, 8242, 8243, 8249, 8250, 8252, 8254, 8260, 8319, 8355, 8356, 8359,
  8364, 8453, 8467, 8470, 8482, 8486, 8494, 8539, 8540, 8541, 8542, 8592, 8593,
  8594, 8595, 8596, 8597, 8616, 8706, 8710, 8719, 8721, 8722, 8730, 8734, 8735,
  8745, 8747, 8776, 8800, 8801, 8804, 8805, 8962, 8976, 8992, 8993, 9472, 9474,
  9484, 9488, 9492, 9496, 9500, 9508, 9516, 9524, 9532, 9552, 9553, 9554, 9555,
  9556, 9557, 9558, 9559, 9560, 9561, 9562, 9563, 9564, 9565, 9566, 9567, 9568,
  9569, 9570, 9571, 9572, 9573, 9574, 9575, 9576, 9577, 9578, 9579, 9580, 9600,
  9604, 9608, 9612, 9616, 9617, 9618, 9619, 9632, 9633, 9642, 9643, 9644, 9650,
  9658, 9660, 9668, 9674, 9675, 9679, 9688, 9689, 9702, 9786, 9787, 9788, 9792,
  9794, 9824, 9827, 9829, 9830, 9834, 9835, 9836, 61441, 61442, 61445, -1, -1,
  -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,
];

// Widths of glyphs in LiberationSans-Italic.ttf.
const LiberationSansItalicWidths = [
  365, 0, 333, 278, 278, 355, 556, 556, 889, 667, 191, 333, 333, 389, 584, 278,
  333, 278, 278, 556, 556, 556, 556, 556, 556, 556, 556, 556, 556, 278, 278,
  584, 584, 584, 556, 1015, 667, 667, 722, 722, 667, 611, 778, 722, 278, 500,
  667, 556, 833, 722, 778, 667, 778, 722, 667, 611, 722, 667, 944, 667, 667,
  611, 278, 278, 278, 469, 556, 333, 556, 556, 500, 556, 556, 278, 556, 556,
  222, 222, 500, 222, 833, 556, 556, 556, 556, 333, 500, 278, 556, 500, 722,
  500, 500, 500, 334, 260, 334, 584, 333, 556, 556, 556, 556, 260, 556, 333,
  737, 370, 556, 584, 737, 552, 400, 549, 333, 333, 333, 576, 537, 278, 333,
  333, 365, 556, 834, 834, 834, 611, 667, 667, 667, 667, 667, 667, 1000, 722,
  667, 667, 667, 667, 278, 278, 278, 278, 722, 722, 778, 778, 778, 778, 778,
  584, 778, 722, 722, 722, 722, 667, 667, 611, 556, 556, 556, 556, 556, 556,
  889, 500, 556, 556, 556, 556, 278, 278, 278, 278, 556, 556, 556, 556, 556,
  556, 556, 549, 611, 556, 556, 556, 556, 500, 556, 500, 667, 556, 667, 556,
  667, 556, 722, 500, 722, 500, 722, 500, 722, 500, 722, 625, 722, 556, 667,
  556, 667, 556, 667, 556, 667, 556, 667, 556, 778, 556, 778, 556, 778, 556,
  778, 556, 722, 556, 722, 556, 278, 278, 278, 278, 278, 278, 278, 222, 278,
  278, 733, 444, 500, 222, 667, 500, 500, 556, 222, 556, 222, 556, 281, 556,
  400, 556, 222, 722, 556, 722, 556, 722, 556, 615, 723, 556, 778, 556, 778,
  556, 778, 556, 1000, 944, 722, 333, 722, 333, 722, 333, 667, 500, 667, 500,
  667, 500, 667, 500, 611, 278, 611, 354, 611, 278, 722, 556, 722, 556, 722,
  556, 722, 556, 722, 556, 722, 556, 944, 722, 667, 500, 667, 611, 500, 611,
  500, 611, 500, 222, 556, 667, 556, 1000, 889, 778, 611, 667, 500, 611, 278,
  333, 333, 333, 333, 333, 333, 333, 333, 333, 333, 333, 667, 278, 789, 846,
  389, 794, 865, 775, 222, 667, 667, 570, 671, 667, 611, 722, 778, 278, 667,
  667, 833, 722, 648, 778, 725, 667, 600, 611, 667, 837, 667, 831, 761, 278,
  667, 570, 439, 555, 222, 550, 570, 571, 500, 556, 439, 463, 555, 542, 222,
  500, 492, 548, 500, 447, 556, 670, 573, 486, 603, 374, 550, 652, 546, 728,
  779, 222, 550, 556, 550, 779, 667, 667, 843, 544, 708, 667, 278, 278, 500,
  1066, 982, 844, 589, 715, 639, 724, 667, 651, 667, 544, 704, 667, 917, 614,
  715, 715, 589, 686, 833, 722, 778, 725, 667, 722, 611, 639, 795, 667, 727,
  673, 920, 923, 805, 886, 651, 694, 1022, 682, 556, 562, 522, 493, 553, 556,
  688, 465, 556, 556, 472, 564, 686, 550, 556, 556, 556, 500, 833, 500, 835,
  500, 572, 518, 830, 851, 621, 736, 526, 492, 752, 534, 556, 556, 556, 378,
  496, 500, 222, 222, 222, 910, 828, 556, 472, 565, 500, 556, 778, 556, 492,
  339, 944, 722, 944, 722, 944, 722, 667, 500, 333, 333, 556, 1000, 1000, 552,
  222, 222, 222, 222, 333, 333, 333, 556, 556, 350, 1000, 1000, 188, 354, 333,
  333, 500, 333, 167, 365, 556, 556, 1094, 556, 885, 323, 1083, 1000, 768, 600,
  834, 834, 834, 834, 1000, 500, 998, 500, 1000, 500, 500, 494, 612, 823, 713,
  584, 549, 713, 979, 719, 274, 549, 549, 584, 549, 549, 604, 584, 604, 604,
  708, 625, 708, 708, 708, 708, 708, 708, 708, 708, 708, 708, 708, 708, 708,
  708, 708, 708, 708, 708, 708, 708, 708, 708, 708, 708, 708, 708, 708, 708,
  708, 708, 708, 708, 708, 708, 708, 708, 708, 708, 708, 708, 708, 708, 708,
  708, 708, 729, 604, 604, 354, 354, 1000, 990, 990, 990, 990, 494, 604, 604,
  604, 604, 354, 1021, 1052, 917, 750, 750, 531, 656, 594, 510, 500, 750, 750,
  500, 500, 333, 333, 333, 333, 333, 333, 333, 333, 222, 222, 294, 294, 324,
  324, 316, 328, 398, 285,
];

// Char code of glyphs in LiberationSans-Italic.ttf.
const LiberationSansItalicMapping = [
  -1, -1, -1, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47,
  48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66,
  67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85,
  86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103,
  104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118,
  119, 120, 121, 122, 123, 124, 125, 126, 161, 162, 163, 164, 165, 166, 167,
  168, 169, 170, 171, 172, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183,
  184, 185, 186, 187, 188, 189, 190, 191, 192, 193, 194, 195, 196, 197, 198,
  199, 200, 201, 202, 203, 204, 205, 206, 207, 208, 209, 210, 211, 212, 213,
  214, 215, 216, 217, 218, 219, 220, 221, 222, 223, 224, 225, 226, 227, 228,
  229, 230, 231, 232, 233, 234, 235, 236, 237, 238, 239, 240, 241, 242, 243,
  244, 245, 246, 247, 248, 249, 250, 251, 252, 253, 254, 255, 256, 257, 258,
  259, 260, 261, 262, 263, 264, 265, 266, 267, 268, 269, 270, 271, 272, 273,
  274, 275, 276, 277, 278, 279, 280, 281, 282, 283, 284, 285, 286, 287, 288,
  289, 290, 291, 292, 293, 294, 295, 296, 297, 298, 299, 300, 301, 302, 303,
  304, 305, 306, 307, 308, 309, 310, 311, 312, 313, 314, 315, 316, 317, 318,
  319, 320, 321, 322, 323, 324, 325, 326, 327, 328, 329, 330, 331, 332, 333,
  334, 335, 336, 337, 338, 339, 340, 341, 342, 343, 344, 345, 346, 347, 348,
  349, 350, 351, 352, 353, 354, 355, 356, 357, 358, 359, 360, 361, 362, 363,
  364, 365, 366, 367, 368, 369, 370, 371, 372, 373, 374, 375, 376, 377, 378,
  379, 380, 381, 382, 383, 402, 506, 507, 508, 509, 510, 511, 536, 537, 538,
  539, 710, 711, 713, 728, 729, 730, 731, 732, 733, 900, 901, 902, 903, 904,
  905, 906, 908, 910, 911, 912, 913, 914, 915, 916, 917, 918, 919, 920, 921,
  922, 923, 924, 925, 926, 927, 928, 929, 931, 932, 933, 934, 935, 936, 937,
  938, 939, 940, 941, 942, 943, 944, 945, 946, 947, 948, 949, 950, 951, 952,
  953, 954, 955, 956, 957, 958, 959, 960, 961, 962, 963, 964, 965, 966, 967,
  968, 969, 970, 971, 972, 973, 974, 1024, 1025, 1026, 1027, 1028, 1029, 1030,
  1031, 1032, 1033, 1034, 1035, 1036, 1037, 1038, 1039, 1040, 1041, 1042, 1043,
  1044, 1045, 1046, 1047, 1048, 1049, 1050, 1051, 1052, 1053, 1054, 1055, 1056,
  1057, 1058, 1059, 1060, 1061, 1062, 1063, 1064, 1065, 1066, 1067, 1068, 1069,
  1070, 1071, 1072, 1073, 1074, 1075, 1076, 1077, 1078, 1079, 1080, 1081, 1082,
  1083, 1084, 1085, 1086, 1087, 1088, 1089, 1090, 1091, 1092, 1093, 1094, 1095,
  1096, 1097, 1098, 1099, 1100, 1101, 1102, 1103, 1104, 1105, 1106, 1107, 1108,
  1109, 1110, 1111, 1112, 1113, 1114, 1115, 1116, 1117, 1118, 1119, 1138, 1139,
  1168, 1169, 7808, 7809, 7810, 7811, 7812, 7813, 7922, 7923, 8208, 8209, 8211,
  8212, 8213, 8215, 8216, 8217, 8218, 8219, 8220, 8221, 8222, 8224, 8225, 8226,
  8230, 8240, 8242, 8243, 8249, 8250, 8252, 8254, 8260, 8319, 8355, 8356, 8359,
  8364, 8453, 8467, 8470, 8482, 8486, 8494, 8539, 8540, 8541, 8542, 8592, 8593,
  8594, 8595, 8596, 8597, 8616, 8706, 8710, 8719, 8721, 8722, 8730, 8734, 8735,
  8745, 8747, 8776, 8800, 8801, 8804, 8805, 8962, 8976, 8992, 8993, 9472, 9474,
  9484, 9488, 9492, 9496, 9500, 9508, 9516, 9524, 9532, 9552, 9553, 9554, 9555,
  9556, 9557, 9558, 9559, 9560, 9561, 9562, 9563, 9564, 9565, 9566, 9567, 9568,
  9569, 9570, 9571, 9572, 9573, 9574, 9575, 9576, 9577, 9578, 9579, 9580, 9600,
  9604, 9608, 9612, 9616, 9617, 9618, 9619, 9632, 9633, 9642, 9643, 9644, 9650,
  9658, 9660, 9668, 9674, 9675, 9679, 9688, 9689, 9702, 9786, 9787, 9788, 9792,
  9794, 9824, 9827, 9829, 9830, 9834, 9835, 9836, 61441, 61442, 61445, -1, -1,
  -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,
];

// Widths of glyphs in LiberationSans-Regular.ttf.
const LiberationSansRegularWidths = [
  365, 0, 333, 278, 278, 355, 556, 556, 889, 667, 191, 333, 333, 389, 584, 278,
  333, 278, 278, 556, 556, 556, 556, 556, 556, 556, 556, 556, 556, 278, 278,
  584, 584, 584, 556, 1015, 667, 667, 722, 722, 667, 611, 778, 722, 278, 500,
  667, 556, 833, 722, 778, 667, 778, 722, 667, 611, 722, 667, 944, 667, 667,
  611, 278, 278, 278, 469, 556, 333, 556, 556, 500, 556, 556, 278, 556, 556,
  222, 222, 500, 222, 833, 556, 556, 556, 556, 333, 500, 278, 556, 500, 722,
  500, 500, 500, 334, 260, 334, 584, 333, 556, 556, 556, 556, 260, 556, 333,
  737, 370, 556, 584, 737, 552, 400, 549, 333, 333, 333, 576, 537, 278, 333,
  333, 365, 556, 834, 834, 834, 611, 667, 667, 667, 667, 667, 667, 1000, 722,
  667, 667, 667, 667, 278, 278, 278, 278, 722, 722, 778, 778, 778, 778, 778,
  584, 778, 722, 722, 722, 722, 667, 667, 611, 556, 556, 556, 556, 556, 556,
  889, 500, 556, 556, 556, 556, 278, 278, 278, 278, 556, 556, 556, 556, 556,
  556, 556, 549, 611, 556, 556, 556, 556, 500, 556, 500, 667, 556, 667, 556,
  667, 556, 722, 500, 722, 500, 722, 500, 722, 500, 722, 615, 722, 556, 667,
  556, 667, 556, 667, 556, 667, 556, 667, 556, 778, 556, 778, 556, 778, 556,
  778, 556, 722, 556, 722, 556, 278, 278, 278, 278, 278, 278, 278, 222, 278,
  278, 735, 444, 500, 222, 667, 500, 500, 556, 222, 556, 222, 556, 292, 556,
  334, 556, 222, 722, 556, 722, 556, 722, 556, 604, 723, 556, 778, 556, 778,
  556, 778, 556, 1000, 944, 722, 333, 722, 333, 722, 333, 667, 500, 667, 500,
  667, 500, 667, 500, 611, 278, 611, 375, 611, 278, 722, 556, 722, 556, 722,
  556, 722, 556, 722, 556, 722, 556, 944, 722, 667, 500, 667, 611, 500, 611,
  500, 611, 500, 222, 556, 667, 556, 1000, 889, 778, 611, 667, 500, 611, 278,
  333, 333, 333, 333, 333, 333, 333, 333, 333, 333, 333, 667, 278, 784, 838,
  384, 774, 855, 752, 222, 667, 667, 551, 668, 667, 611, 722, 778, 278, 667,
  668, 833, 722, 650, 778, 722, 667, 618, 611, 667, 798, 667, 835, 748, 278,
  667, 578, 446, 556, 222, 547, 578, 575, 500, 557, 446, 441, 556, 556, 222,
  500, 500, 576, 500, 448, 556, 690, 569, 482, 617, 395, 547, 648, 525, 713,
  781, 222, 547, 556, 547, 781, 667, 667, 865, 542, 719, 667, 278, 278, 500,
  1057, 1010, 854, 583, 722, 635, 719, 667, 656, 667, 542, 677, 667, 923, 604,
  719, 719, 583, 656, 833, 722, 778, 719, 667, 722, 611, 635, 760, 667, 740,
  667, 917, 938, 792, 885, 656, 719, 1010, 722, 556, 573, 531, 365, 583, 556,
  669, 458, 559, 559, 438, 583, 688, 552, 556, 542, 556, 500, 458, 500, 823,
  500, 573, 521, 802, 823, 625, 719, 521, 510, 750, 542, 556, 556, 556, 365,
  510, 500, 222, 278, 222, 906, 812, 556, 438, 559, 500, 552, 778, 556, 489,
  411, 944, 722, 944, 722, 944, 722, 667, 500, 333, 333, 556, 1000, 1000, 552,
  222, 222, 222, 222, 333, 333, 333, 556, 556, 350, 1000, 1000, 188, 354, 333,
  333, 500, 333, 167, 365, 556, 556, 1094, 556, 885, 323, 1073, 1000, 768, 600,
  834, 834, 834, 834, 1000, 500, 1000, 500, 1000, 500, 500, 494, 612, 823, 713,
  584, 549, 713, 979, 719, 274, 549, 549, 583, 549, 549, 604, 584, 604, 604,
  708, 625, 708, 708, 708, 708, 708, 708, 708, 708, 708, 708, 708, 708, 708,
  708, 708, 708, 708, 708, 708, 708, 708, 708, 708, 708, 708, 708, 708, 708,
  708, 708, 708, 708, 708, 708, 708, 708, 708, 708, 708, 708, 708, 708, 708,
  708, 708, 729, 604, 604, 354, 354, 1000, 990, 990, 990, 990, 494, 604, 604,
  604, 604, 354, 1021, 1052, 917, 750, 750, 531, 656, 594, 510, 500, 750, 750,
  500, 500, 333, 333, 333, 333, 333, 333, 333, 333, 222, 222, 294, 294, 324,
  324, 316, 328, 398, 285,
];

// Char code of glyphs in LiberationSans-Regular.ttf.
const LiberationSansRegularMapping = [
  -1, -1, -1, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47,
  48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66,
  67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85,
  86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103,
  104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118,
  119, 120, 121, 122, 123, 124, 125, 126, 161, 162, 163, 164, 165, 166, 167,
  168, 169, 170, 171, 172, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183,
  184, 185, 186, 187, 188, 189, 190, 191, 192, 193, 194, 195, 196, 197, 198,
  199, 200, 201, 202, 203, 204, 205, 206, 207, 208, 209, 210, 211, 212, 213,
  214, 215, 216, 217, 218, 219, 220, 221, 222, 223, 224, 225, 226, 227, 228,
  229, 230, 231, 232, 233, 234, 235, 236, 237, 238, 239, 240, 241, 242, 243,
  244, 245, 246, 247, 248, 249, 250, 251, 252, 253, 254, 255, 256, 257, 258,
  259, 260, 261, 262, 263, 264, 265, 266, 267, 268, 269, 270, 271, 272, 273,
  274, 275, 276, 277, 278, 279, 280, 281, 282, 283, 284, 285, 286, 287, 288,
  289, 290, 291, 292, 293, 294, 295, 296, 297, 298, 299, 300, 301, 302, 303,
  304, 305, 306, 307, 308, 309, 310, 311, 312, 313, 314, 315, 316, 317, 318,
  319, 320, 321, 322, 323, 324, 325, 326, 327, 328, 329, 330, 331, 332, 333,
  334, 335, 336, 337, 338, 339, 340, 341, 342, 343, 344, 345, 346, 347, 348,
  349, 350, 351, 352, 353, 354, 355, 356, 357, 358, 359, 360, 361, 362, 363,
  364, 365, 366, 367, 368, 369, 370, 371, 372, 373, 374, 375, 376, 377, 378,
  379, 380, 381, 382, 383, 402, 506, 507, 508, 509, 510, 511, 536, 537, 538,
  539, 710, 711, 713, 728, 729, 730, 731, 732, 733, 900, 901, 902, 903, 904,
  905, 906, 908, 910, 911, 912, 913, 914, 915, 916, 917, 918, 919, 920, 921,
  922, 923, 924, 925, 926, 927, 928, 929, 931, 932, 933, 934, 935, 936, 937,
  938, 939, 940, 941, 942, 943, 944, 945, 946, 947, 948, 949, 950, 951, 952,
  953, 954, 955, 956, 957, 958, 959, 960, 961, 962, 963, 964, 965, 966, 967,
  968, 969, 970, 971, 972, 973, 974, 1024, 1025, 1026, 1027, 1028, 1029, 1030,
  1031, 1032, 1033, 1034, 1035, 1036, 1037, 1038, 1039, 1040, 1041, 1042, 1043,
  1044, 1045, 1046, 1047, 1048, 1049, 1050, 1051, 1052, 1053, 1054, 1055, 1056,
  1057, 1058, 1059, 1060, 1061, 1062, 1063, 1064, 1065, 1066, 1067, 1068, 1069,
  1070, 1071, 1072, 1073, 1074, 1075, 1076, 1077, 1078, 1079, 1080, 1081, 1082,
  1083, 1084, 1085, 1086, 1087, 1088, 1089, 1090, 1091, 1092, 1093, 1094, 1095,
  1096, 1097, 1098, 1099, 1100, 1101, 1102, 1103, 1104, 1105, 1106, 1107, 1108,
  1109, 1110, 1111, 1112, 1113, 1114, 1115, 1116, 1117, 1118, 1119, 1138, 1139,
  1168, 1169, 7808, 7809, 7810, 7811, 7812, 7813, 7922, 7923, 8208, 8209, 8211,
  8212, 8213, 8215, 8216, 8217, 8218, 8219, 8220, 8221, 8222, 8224, 8225, 8226,
  8230, 8240, 8242, 8243, 8249, 8250, 8252, 8254, 8260, 8319, 8355, 8356, 8359,
  8364, 8453, 8467, 8470, 8482, 8486, 8494, 8539, 8540, 8541, 8542, 8592, 8593,
  8594, 8595, 8596, 8597, 8616, 8706, 8710, 8719, 8721, 8722, 8730, 8734, 8735,
  8745, 8747, 8776, 8800, 8801, 8804, 8805, 8962, 8976, 8992, 8993, 9472, 9474,
  9484, 9488, 9492, 9496, 9500, 9508, 9516, 9524, 9532, 9552, 9553, 9554, 9555,
  9556, 9557, 9558, 9559, 9560, 9561, 9562, 9563, 9564, 9565, 9566, 9567, 9568,
  9569, 9570, 9571, 9572, 9573, 9574, 9575, 9576, 9577, 9578, 9579, 9580, 9600,
  9604, 9608, 9612, 9616, 9617, 9618, 9619, 9632, 9633, 9642, 9643, 9644, 9650,
  9658, 9660, 9668, 9674, 9675, 9679, 9688, 9689, 9702, 9786, 9787, 9788, 9792,
  9794, 9824, 9827, 9829, 9830, 9834, 9835, 9836, 61441, 61442, 61445, -1, -1,
  -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,
];

export {
  LiberationSansBoldItalicMapping,
  LiberationSansBoldItalicWidths,
  LiberationSansBoldMapping,
  LiberationSansBoldWidths,
  LiberationSansItalicMapping,
  LiberationSansItalicWidths,
  LiberationSansRegularMapping,
  LiberationSansRegularWidths,
};
