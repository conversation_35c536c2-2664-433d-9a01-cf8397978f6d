<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vue 演示测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-section {
            background: #f5f5f5;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .link-list {
            list-style: none;
            padding: 0;
        }
        .link-list li {
            margin: 10px 0;
        }
        .link-list a {
            display: inline-block;
            padding: 10px 15px;
            background: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            transition: background 0.2s;
        }
        .link-list a:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <h1>Vue 演示页面测试</h1>
    
    <div class="test-section success">
        <h2>✅ 测试成功</h2>
        <p>如果您能看到这个页面，说明 Vite 开发服务器已经正确配置，可以访问 example 目录下的文件。</p>
    </div>
    
    <div class="test-section">
        <h2>📋 可用的演示页面</h2>
        <ul class="link-list">
            <li>
                <a href="/dev-index.html" target="_blank">
                    🏠 开发首页 - 包含所有演示链接
                </a>
            </li>
            <li>
                <a href="/example/vue-demo/index.html" target="_blank">
                    📄 基础演示 - 简单的 Vue 组件演示
                </a>
            </li>
            <li>
                <a href="/example/vue-demo/vue-app.html" target="_blank">
                    ⚡ 完整演示 - 功能完整的演示应用
                </a>
            </li>
            <li>
                <a href="/index.html" target="_blank">
                    🔧 原生演示 - 基于原生 JavaScript 的演示
                </a>
            </li>
            <li>
                <a href="/tools.html" target="_blank">
                    🛠️ 工具页面 - 开发者工具和测试功能
                </a>
            </li>
        </ul>
    </div>
    
    <div class="test-section">
        <h2>🎯 使用说明</h2>
        <ol>
            <li><strong>基础演示</strong>：适合快速了解 OFD Viewer 组件的基本功能</li>
            <li><strong>完整演示</strong>：包含详细的控制面板和状态监控，适合深入了解组件特性</li>
            <li><strong>原生演示</strong>：展示 liteofd 核心库的直接使用方法</li>
            <li><strong>工具页面</strong>：提供开发和调试功能</li>
        </ol>
    </div>
    
    <div class="test-section">
        <h2>📝 注意事项</h2>
        <ul>
            <li>演示页面使用 Vue 3 CDN，无需额外安装</li>
            <li>包含模拟的 OFD Viewer 组件，展示完整的 API 和功能</li>
            <li>可以上传真实的 OFD 文件进行测试（需要集成真实组件）</li>
            <li>所有演示都是响应式设计，支持移动端访问</li>
        </ul>
    </div>
    
    <div class="test-section">
        <h2>🔧 开发集成</h2>
        <p>要在实际项目中使用 OFD Viewer 组件：</p>
        <ol>
            <li>复制 <code>src/ofdViewer/ofdViewer.vue</code> 到你的项目</li>
            <li>参考 <code>integration-example.vue</code> 进行集成</li>
            <li>确保安装了必要的依赖包</li>
            <li>根据需要自定义样式和功能</li>
        </ol>
    </div>
    
    <script>
        // 简单的页面加载测试
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Vue 演示测试页面加载完成');
            console.log('当前时间:', new Date().toLocaleString());
            console.log('页面 URL:', window.location.href);
        });
    </script>
</body>
</html>
