{
  dict: {
    loadedName: "g_d0_f1",
    objId: "6R",
    suppressEncryption: false,
    xref: {
      entries: [
        {
          "offset": 0,
          "gen": 65535,
          "free": true
        },
        {
          "offset": 15,
          "gen": 0,
          "uncompressed": true
        },
        {
          "offset": 280,
          "gen": 0,
          "uncompressed": true
        },
        {
          "offset": 386,
          "gen": 0,
          "uncompressed": true
        },
        {
          "offset": 5083,
          "gen": 0,
          "uncompressed": true
        },
        {
          "offset": 5441,
          "gen": 0,
          "uncompressed": true
        },
        {
          "offset": 20506,
          "gen": 0,
          "uncompressed": true
        },
        {
          "offset": 20668,
          "gen": 0,
          "uncompressed": true
        },
        {
          "offset": 21199,
          "gen": 0,
          "uncompressed": true
        },
        {
          "offset": 41384,
          "gen": 0,
          "uncompressed": true
        },
        {
          "offset": 41545,
          "gen": 0,
          "uncompressed": true
        },
        {
          "offset": 41752,
          "gen": 0,
          "uncompressed": true
        },
        {
          "offset": 43202,
          "gen": 0,
          "uncompressed": true
        },
        {
          "offset": 43351,
          "gen": 0,
          "uncompressed": true
        },
        {
          "offset": 43554,
          "gen": 0,
          "uncompressed": true
        },
        {
          "offset": 45797,
          "gen": 0,
          "uncompressed": true
        },
        {
          "offset": 45946,
          "gen": 0,
          "uncompressed": true
        },
        {
          "offset": 46145,
          "gen": 0,
          "uncompressed": true
        },
        {
          "offset": 47529,
          "gen": 0,
          "uncompressed": true
        },
        {
          "offset": 47677,
          "gen": 0,
          "uncompressed": true
        },
        {
          "offset": 47891,
          "gen": 0,
          "uncompressed": true
        },
        {
          "offset": 49545,
          "gen": 0,
          "uncompressed": true
        },
        {
          "offset": 49695,
          "gen": 0,
          "uncompressed": true
        },
        {
          "offset": 50142,
          "gen": 0,
          "uncompressed": true
        },
        {
          "offset": 60591,
          "gen": 0,
          "uncompressed": true
        },
        {
          "offset": 60742,
          "gen": 0,
          "uncompressed": true
        },
        {
          "offset": 60979,
          "gen": 0,
          "uncompressed": true
        },
        {
          "offset": 64435,
          "gen": 0,
          "uncompressed": true
        },
        {
          "offset": 64584,
          "gen": 0,
          "uncompressed": true
        },
        {
          "offset": 64837,
          "gen": 0,
          "uncompressed": true
        },
        {
          "offset": 74560,
          "gen": 0,
          "uncompressed": true
        },
        {
          "offset": 74728,
          "gen": 0,
          "uncompressed": true
        },
        {
          "offset": 75095,
          "gen": 0,
          "uncompressed": true
        },
        {
          "offset": 89543,
          "gen": 0,
          "uncompressed": true
        },
        {
          "offset": 89710,
          "gen": 0,
          "uncompressed": true
        },
        {
          "offset": 89951,
          "gen": 0,
          "uncompressed": true
        },
        {
          "offset": 98060,
          "gen": 0,
          "uncompressed": true
        },
        {
          "offset": 98233,
          "gen": 0,
          "uncompressed": true
        },
        {
          "offset": 98433,
          "gen": 0,
          "uncompressed": true
        },
        {
          "offset": 99732,
          "gen": 0,
          "uncompressed": true
        },
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        {
          "offset": 99882,
          "gen": 0,
          "uncompressed": true
        },
        {
          "offset": 99970,
          "gen": 0,
          "uncompressed": true
        },
        {
          "offset": 99996,
          "gen": 0,
          "uncompressed": true
        },
        {
          "offset": 101879,
          "gen": 0,
          "uncompressed": true
        },
        {
          "offset": 102000,
          "gen": 0,
          "uncompressed": true
        },
        {
          "offset": 102369,
          "gen": 0,
          "uncompressed": true
        },
        {
          "offset": 102550,
          "gen": 0,
          "uncompressed": true
        },
        {
          "offset": 102710,
          "gen": 0,
          "uncompressed": true
        },
        {
          "offset": 102794,
          "gen": 0,
          "uncompressed": true
        },
        {
          "offset": 103336,
          "gen": 0,
          "uncompressed": true
        },
        {
          "offset": 103667,
          "gen": 0,
          "uncompressed": true
        },
        {
          "offset": 103773,
          "gen": 0,
          "uncompressed": true
        },
        {
          "offset": 104008,
          "gen": 0,
          "uncompressed": true
        },
        {
          "offset": 104094,
          "gen": 0,
          "uncompressed": true
        },
        {
          "offset": 104119,
          "gen": 0,
          "uncompressed": true
        },
        {
          "offset": 104231,
          "gen": 0,
          "uncompressed": true
        },
        {
          "offset": 104276,
          "gen": 0,
          "uncompressed": true
        },
        {
          "offset": 104383,
          "gen": 0,
          "uncompressed": true
        },
        {
          "offset": 104434,
          "gen": 0,
          "uncompressed": true
        },
        {
          "offset": 104958,
          "gen": 0,
          "uncompressed": true
        },
        {
          "offset": 105252,
          "gen": 0,
          "uncompressed": true
        },
        {
          "offset": 105335,
          "gen": 0,
          "uncompressed": true
        },
        {
          "offset": 105391,
          "gen": 0,
          "uncompressed": true
        },
        null,
        null,
        null,
        {
          "offset": 105522,
          "gen": 0,
          "uncompressed": true
        },
        {
          "offset": 106576,
          "gen": 0,
          "uncompressed": true
        },
        {
          "offset": 106738,
          "gen": 0,
          "uncompressed": true
        },
        {
          "offset": 107792,
          "gen": 0,
          "uncompressed": true
        },
        {
          "offset": 107954,
          "gen": 0,
          "uncompressed": true
        },
        {
          "offset": 108141,
          "gen": 0,
          "uncompressed": true
        },
        {
          "offset": 108252,
          "gen": 0,
          "uncompressed": true
        },
        {
          "offset": 108409,
          "gen": 0,
          "uncompressed": true
        },
        {
          "offset": 109498,
          "gen": 0,
          "uncompressed": true
        },
        {
          "offset": 109655,
          "gen": 0,
          "uncompressed": true
        },
        {
          "offset": 110716,
          "gen": 0,
          "uncompressed": true
        },
        null,
        {
          "offset": 110888,
          "gen": 0,
          "uncompressed": true
        },
        {
          "offset": 121348,
          "gen": 0,
          "uncompressed": true
        },
        {
          "offset": 121747,
          "gen": 0,
          "uncompressed": true
        },
        {
          "offset": 121905,
          "gen": 0,
          "uncompressed": true
        },
        {
          "offset": 121954,
          "gen": 0,
          "uncompressed": true
        },
        {
          "offset": 122431,
          "gen": 0,
          "uncompressed": true
        },
        {
          "offset": 134614,
          "gen": 0,
          "uncompressed": true
        },
        {
          "offset": 134669,
          "gen": 0,
          "uncompressed": true
        },
        {
          "offset": 134745,
          "gen": 0,
          "uncompressed": true
        },
        {
          "offset": 134824,
          "gen": 0,
          "uncompressed": true
        },
        {
          "offset": 134866,
          "gen": 0,
          "uncompressed": true
        },
        {
          "offset": 135185,
          "gen": 0,
          "uncompressed": true
        },
        {
          "offset": 135448,
          "gen": 0,
          "uncompressed": true
        },
        {
          "offset": 136502,
          "gen": 0,
          "uncompressed": true
        },
        {
          "offset": 136664,
          "gen": 0,
          "uncompressed": true
        }
      ],
      pdfManager: null,
      root: null,
      startXRefQueue: [],
      stream: null,
      topDict: null,
      trailer: null,
    },
    _map: {
      "Type": {
        "name": "Font"
      },
      "Subtype": {
        "name": "Type1"
      },
      "Encoding": {
        "num": 976,
        "gen": 0
      },
      "FirstChar": 45,
      "LastChar": 122,
      "Widths": {
        "num": 993,
        "gen": 0
      },
      "BaseFont": {
        "name": "TACTGM+NimbusRomNo9L-Medi"
      },
      "FontDescriptor": {
        "num": 4,
        "gen": 0
      }
    }
  },
  hasEncoding: true,
  hasIncludedToUnicodeMap: false,
  type: "Type1",
  subtype: null,
  file: {
    buffer: null,
    bufferLength: 0,
    dict: {
      objId: "5R"
    }
  },
  length1: 1626,
  length2: 14082,
  length3: 532,
  isInternalFont: false,
  composite: false,
  fixedPitch: false,
  differences: [
    ".notdef",
    "dotaccent",
    "fi",
    "fl",
    "fraction",
    "hungarumlaut",
    "Lslash",
    "lslash",
    "ogonek",
    "ring",
    ".notdef",
    "breve",
    "minus",
    ".notdef",
    "Zcaron",
    "zcaron",
    "caron",
    "dotlessi",
    "dotlessj",
    "ff",
    "ffi",
    "ffl",
    "notequal",
    "infinity",
    "lessequal",
    "greaterequal",
    "partialdiff",
    "summation",
    "product",
    "pi",
    "grave",
    "quotesingle",
    "space",
    "exclam",
    "quotedbl",
    "numbersign",
    "dollar",
    "percent",
    "ampersand",
    "quoteright",
    "parenleft",
    "parenright",
    "asterisk",
    "plus",
    "comma",
    "hyphen",
    "period",
    "slash",
    "zero",
    "one",
    "two",
    "three",
    "four",
    "five",
    "six",
    "seven",
    "eight",
    "nine",
    "colon",
    "semicolon",
    "less",
    "equal",
    "greater",
    "question",
    "at",
    "A",
    "B",
    "C",
    "D",
    "E",
    "F",
    "G",
    "H",
    "I",
    "J",
    "K",
    "L",
    "M",
    "N",
    "O",
    "P",
    "Q",
    "R",
    "S",
    "T",
    "U",
    "V",
    "W",
    "X",
    "Y",
    "Z",
    "bracketleft",
    "backslash",
    "bracketright",
    "asciicircum",
    "underscore",
    "quoteleft",
    "a",
    "b",
    "c",
    "d",
    "e",
    "f",
    "g",
    "h",
    "i",
    "j",
    "k",
    "l",
    "m",
    "n",
    "o",
    "p",
    "q",
    "r",
    "s",
    "t",
    "u",
    "v",
    "w",
    "x",
    "y",
    "z",
    "braceleft",
    "bar",
    "braceright",
    "asciitilde",
    ".notdef",
    "Euro",
    "integral",
    "quotesinglbase",
    "florin",
    "quotedblbase",
    "ellipsis",
    "dagger",
    "daggerdbl",
    "circumflex",
    "perthousand",
    "Scaron",
    "guilsinglleft",
    "OE",
    "Omega",
    "radical",
    "approxequal",
    ".notdef",
    null,
    null,
    "quotedblleft",
    "quotedblright",
    "bullet",
    "endash",
    "emdash",
    "tilde",
    "trademark",
    "scaron",
    "guilsinglright",
    "oe",
    "Delta",
    "lozenge",
    "Ydieresis",
    ".notdef",
    "exclamdown",
    "cent",
    "sterling",
    "currency",
    "yen",
    "brokenbar",
    "section",
    "dieresis",
    "copyright",
    "ordfeminine",
    "guillemotleft",
    "logicalnot",
    "hyphen",
    "registered",
    "macron",
    "degree",
    "plusminus",
    "twosuperior",
    "threesuperior",
    "acute",
    "mu",
    "paragraph",
    "periodcentered",
    "cedilla",
    "onesuperior",
    "ordmasculine",
    "guillemotright",
    "onequarter",
    "onehalf",
    "threequarters",
    "questiondown",
    "Agrave",
    "Aacute",
    "Acircumflex",
    "Atilde",
    "Adieresis",
    "Aring",
    "AE",
    "Ccedilla",
    "Egrave",
    "Eacute",
    "Ecircumflex",
    "Edieresis",
    "Igrave",
    "Iacute",
    "Icircumflex",
    "Idieresis",
    "Eth",
    "Ntilde",
    "Ograve",
    "Oacute",
    "Ocircumflex",
    "Otilde",
    "Odieresis",
    "multiply",
    "Oslash",
    "Ugrave",
    "Uacute",
    "Ucircumflex",
    "Udieresis",
    "Yacute",
    "Thorn",
    "germandbls",
    "agrave",
    "aacute",
    "acircumflex",
    "atilde",
    "adieresis",
    "aring",
    "ae",
    "ccedilla",
    "egrave",
    "eacute",
    "ecircumflex",
    "edieresis",
    "igrave",
    "iacute",
    "icircumflex",
    "idieresis",
    "eth",
    "ntilde",
    "ograve",
    "oacute",
    "ocircumflex",
    "otilde",
    "odieresis",
    "divide",
    "oslash",
    "ugrave",
    "uacute",
    "ucircumflex",
    "udieresis",
    "yacute",
    "thorn",
    "ydieresis"
  ],
  fontMatrix: [
    0.001,
    0,
    0,
    0.001,
    0,
    0
  ],
  firstChar: 45,
  lastChar: 122,
  toUnicode: {
    "_map": [
      null,
      "˙",
      "ﬁ",
      "ﬂ",
      "⁄",
      "˝",
      "Ł",
      "ł",
      "˛",
      "˚",
      null,
      "˘",
      "−",
      null,
      "Ž",
      "ž",
      "ˇ",
      "ı",
      "",
      "ﬀ",
      "ﬃ",
      "ﬄ",
      "≠",
      "∞",
      "≤",
      "≥",
      "∂",
      "∑",
      "∏",
      "π",
      "`",
      "'",
      " ",
      "!",
      "\"",
      "#",
      "$",
      "%",
      "&",
      "’",
      "(",
      ")",
      "*",
      "+",
      ",",
      "-",
      ".",
      "/",
      "0",
      "1",
      "2",
      "3",
      "4",
      "5",
      "6",
      "7",
      "8",
      "9",
      ":",
      ";",
      "<",
      "=",
      ">",
      "?",
      "@",
      "A",
      "B",
      "C",
      "D",
      "E",
      "F",
      "G",
      "H",
      "I",
      "J",
      "K",
      "L",
      "M",
      "N",
      "O",
      "P",
      "Q",
      "R",
      "S",
      "T",
      "U",
      "V",
      "W",
      "X",
      "Y",
      "Z",
      "[",
      "\\",
      "]",
      "^",
      "_",
      "‘",
      "a",
      "b",
      "c",
      "d",
      "e",
      "f",
      "g",
      "h",
      "i",
      "j",
      "k",
      "l",
      "m",
      "n",
      "o",
      "p",
      "q",
      "r",
      "s",
      "t",
      "u",
      "v",
      "w",
      "x",
      "y",
      "z",
      "{",
      "|",
      "}",
      "~",
      null,
      "€",
      "∫",
      "‚",
      "ƒ",
      "„",
      "…",
      "†",
      "‡",
      "ˆ",
      "‰",
      "Š",
      "‹",
      "Œ",
      "Ω",
      "√",
      "≈",
      "ê",
      "ë",
      "í",
      "“",
      "”",
      "•",
      "–",
      "—",
      "˜",
      "™",
      "š",
      "›",
      "œ",
      "∆",
      "◊",
      "Ÿ",
      "†",
      "¡",
      "¢",
      "£",
      "¤",
      "¥",
      "¦",
      "§",
      "¨",
      "©",
      "ª",
      "«",
      "¬",
      "-",
      "®",
      "¯",
      "°",
      "±",
      "²",
      "³",
      "´",
      "µ",
      "¶",
      "·",
      "¸",
      "¹",
      "º",
      "»",
      "¼",
      "½",
      "¾",
      "¿",
      "À",
      "Á",
      "Â",
      "Ã",
      "Ä",
      "Å",
      "Æ",
      "Ç",
      "È",
      "É",
      "Ê",
      "Ë",
      "Ì",
      "Í",
      "Î",
      "Ï",
      "Ð",
      "Ñ",
      "Ò",
      "Ó",
      "Ô",
      "Õ",
      "Ö",
      "×",
      "Ø",
      "Ù",
      "Ú",
      "Û",
      "Ü",
      "Ý",
      "Þ",
      "ß",
      "à",
      "á",
      "â",
      "ã",
      "ä",
      "å",
      "æ",
      "ç",
      "è",
      "é",
      "ê",
      "ë",
      "ì",
      "í",
      "î",
      "ï",
      "ð",
      "ñ",
      "ò",
      "ó",
      "ô",
      "õ",
      "ö",
      "÷",
      "ø",
      "ù",
      "ú",
      "û",
      "ü",
      "ý",
      "þ",
      "ÿ"
    ]
  },
  bbox: [-168, -341, 1000, 960],
  ascent: 690,
  descent: -209,
  xHeight: 461,
  capHeight: 690,
  flags: 4,
  italicAngle: 0,
  isType3Font: false,
  cssFontInfo: null,
  scaleFactors: null,
  systemFontInfo: null,
  loadedName: "g_d0_f1",
  name: "TACTGM+NimbusRomNo9L-Medi",
  vmetrics: [],
  widths: [
    null,
    null,
    null,
    null,
    null,
    null,
    null,
    null,
    null,
    null,
    null,
    null,
    null,
    null,
    null,
    null,
    null,
    null,
    null,
    null,
    null,
    null,
    null,
    null,
    null,
    null,
    null,
    null,
    null,
    null,
    null,
    null,
    null,
    null,
    null,
    null,
    null,
    null,
    null,
    null,
    null,
    null,
    null,
    null,
    null,
    333,
    250,
    0,
    500,
    500,
    500,
    500,
    500,
    500,
    500,
    500,
    500,
    500,
    333,
    0,
    0,
    570,
    0,
    0,
    0,
    722,
    667,
    722,
    722,
    667,
    611,
    778,
    0,
    389,
    500,
    0,
    667,
    944,
    722,
    778,
    611,
    0,
    722,
    556,
    667,
    0,
    722,
    1000,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    500,
    556,
    444,
    556,
    444,
    333,
    500,
    556,
    278,
    333,
    556,
    278,
    833,
    556,
    500,
    556,
    0,
    444,
    389,
    333,
    556,
    500,
    722,
    500,
    500,
    444
  ],

}
