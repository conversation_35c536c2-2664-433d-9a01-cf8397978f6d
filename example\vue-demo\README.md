# OFD Viewer Vue 演示

这个目录包含了使用 `ofdViewer` Vue 组件的完整演示示例。

## 文件说明

### 1. `index.html` - 基础演示页面
- 简单的 OFD Viewer 组件演示
- 包含基本的文件上传和查看功能
- 展示组件的主要特性

### 2. `vue-app.html` - 完整演示应用
- 功能完整的演示应用
- 包含详细的控制面板
- 实时状态监控
- 事件日志记录
- 响应式设计

### 3. `main.js` - 基础演示脚本
- 配合 `index.html` 使用
- 包含简化的组件实现

### 4. `vue-app.js` - 完整演示脚本
- 配合 `vue-app.html` 使用
- 包含完整的应用逻辑和模拟组件

### 5. `integration-example.vue` - 真实集成示例
- 可直接在 Vue 项目中使用的完整示例
- 展示如何正确集成 `ofdViewer.vue` 组件
- 包含完整的 TypeScript 类型支持
- 适合作为实际项目的参考模板

## 如何运行演示

### 方法一：直接在浏览器中打开
1. 直接在浏览器中打开 `index.html` 或 `vue-app.html`
2. 由于使用了 CDN 引入 Vue 3，可以直接运行

### 方法二：使用本地服务器
```bash
# 在项目根目录运行
npm run dev

# 然后访问以下地址：
# 开发首页（包含所有演示链接）
http://localhost:3000/dev-index.html

# 基础演示页面
http://localhost:3000/example/vue-demo/index.html

# 完整演示应用
http://localhost:3000/example/vue-demo/vue-app.html

# 原生演示页面
http://localhost:3000/index.html
```

### 方法三：使用简单的 HTTP 服务器
```bash
# 在 vue-demo 目录下
python -m http.server 8000
# 或
npx serve .

# 然后访问 http://localhost:8000
```

## 功能特性

### OFD Viewer 组件特性
- ✅ **文件上传**: 支持本地 OFD 文件选择和上传
- ✅ **文档渲染**: 高质量的 OFD 文档显示
- ✅ **页面导航**: 首页、上一页、下一页、末页导航
- ✅ **缩放控制**: 放大、缩小、重置缩放功能
- ✅ **工具栏**: 可自定义的工具栏界面
- ✅ **事件回调**: 完整的事件监听和回调机制
- ✅ **响应式设计**: 适配不同屏幕尺寸
- ✅ **数字签名**: 支持数字签名识别和交互

### 演示应用特性
- 📁 **文件管理**: 拖拽上传、文件信息显示
- 🎛️ **控制面板**: 完整的功能控制界面
- 📊 **状态监控**: 实时显示文档状态信息
- 📝 **事件日志**: 详细的操作日志记录
- 🎨 **美观界面**: 现代化的用户界面设计
- 📱 **移动端适配**: 响应式布局支持

## 组件 API

### Props
```typescript
interface Props {
  src?: string | File | ArrayBuffer  // OFD文件源
  showToolbar?: boolean              // 是否显示工具栏
  pageWrapStyle?: string            // 页面包装样式
  pageIndexes?: number[]            // 指定渲染的页面
  autoLoad?: boolean                // 是否自动加载
  scale?: number                    // 初始缩放比例
}
```

### Events
```typescript
interface Emits {
  (e: 'loaded', document: OfdDocument): void      // 文档加载完成
  (e: 'error', error: Error): void               // 加载错误
  (e: 'pageChange', page: number): void          // 页面变化
  (e: 'scaleChange', scale: number): void        // 缩放变化
  (e: 'signatureClick', data: any): void         // 签名点击
}
```

### 公开方法
```typescript
// 文件操作
loadFile(file: string | File | ArrayBuffer): Promise<void>

// 页面导航
goToPage(page: number): void
firstPage(): void
prevPage(): void
nextPage(): void
lastPage(): void

// 缩放控制
zoomIn(): void
zoomOut(): void
resetZoom(): void
setZoom(scale: number): void

// 内容操作
search(keyword: string): void
getContent(page?: number): string

// 状态获取
getCurrentPage(): number
getTotalPages(): number
getScale(): number
getDocument(): OfdDocument
```

## 使用示例

### 基础使用
```vue
<template>
  <ofd-viewer
    :src="ofdFile"
    :show-toolbar="true"
    :auto-load="true"
    @loaded="onLoaded"
    @error="onError"
  />
</template>

<script setup>
import OfdViewer from '@/components/OfdViewer.vue'

const ofdFile = ref(null)

const onLoaded = (document) => {
  console.log('文档加载成功', document)
}

const onError = (error) => {
  console.error('加载失败', error)
}
</script>
```

### 高级使用
```vue
<template>
  <ofd-viewer
    ref="viewerRef"
    :src="ofdFile"
    :show-toolbar="showToolbar"
    :page-indexes="[1, 3, 5]"
    :scale="initialScale"
    @page-change="onPageChange"
    @scale-change="onScaleChange"
    @signature-click="onSignatureClick"
  />
</template>

<script setup>
const viewerRef = ref()
const showToolbar = ref(true)
const initialScale = ref(1.2)

// 程序化控制
const zoomToFit = () => {
  viewerRef.value?.setZoom(1)
}

const goToSpecificPage = (page) => {
  viewerRef.value?.goToPage(page)
}

const searchInDocument = (keyword) => {
  viewerRef.value?.search(keyword)
}
</script>
```

## 注意事项

1. **文件格式**: 仅支持 `.ofd` 格式的文件
2. **浏览器兼容性**: 需要现代浏览器支持 ES6+ 特性
3. **Vue 版本**: 需要 Vue 3.x 版本
4. **依赖**: 依赖 `liteofd` 核心库

## 开发说明

### 集成到实际项目

#### 方法一：直接使用集成示例
1. 将 `integration-example.vue` 复制到你的项目中
2. 根据需要修改样式和功能
3. 确保正确导入 `ofdViewer.vue` 组件

#### 方法二：手动集成
1. 将 `src/ofdViewer/ofdViewer.vue` 复制到你的项目中
2. 确保安装了必要的依赖
3. 在你的 Vue 组件中导入和使用

```vue
<script setup>
import OfdViewer from '@/components/OfdViewer.vue'
</script>
```

### 自定义样式
组件支持通过 CSS 变量和类名进行样式自定义：

```css
.ofd-viewer {
  --toolbar-bg: #f8f9fa;
  --toolbar-border: #dee2e6;
  --button-hover: #e9ecef;
}
```

### 扩展功能
可以通过以下方式扩展组件功能：
- 添加新的工具栏按钮
- 实现自定义的页面渲染样式
- 集成搜索和标注功能
- 添加打印和导出功能

## 问题反馈

如果在使用过程中遇到问题，请：
1. 检查浏览器控制台的错误信息
2. 确认 OFD 文件格式正确
3. 验证 Vue 和相关依赖版本
4. 查看演示页面的实现代码作为参考
