<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OFD Viewer Vue 演示</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
        }
        
        .demo-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .demo-header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .demo-title {
            font-size: 2.5rem;
            color: #333;
            margin-bottom: 10px;
        }
        
        .demo-subtitle {
            font-size: 1.2rem;
            color: #666;
            margin-bottom: 20px;
        }
        
        .demo-description {
            max-width: 800px;
            margin: 0 auto;
            line-height: 1.6;
            color: #555;
        }
        
        .demo-section {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            overflow: hidden;
        }
        
        .section-header {
            background: #4a90e2;
            color: white;
            padding: 15px 20px;
            font-size: 1.3rem;
            font-weight: 600;
        }
        
        .section-content {
            padding: 20px;
        }
        
        .viewer-container {
            height: 600px;
            border: 1px solid #ddd;
            border-radius: 4px;
            overflow: hidden;
        }
        
        .controls-panel {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        
        .control-group {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .control-label {
            font-weight: 500;
            color: #333;
        }
        
        .btn {
            padding: 8px 16px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background: white;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .btn:hover {
            background: #f0f0f0;
            border-color: #4a90e2;
        }
        
        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        
        .btn-primary {
            background: #4a90e2;
            color: white;
            border-color: #4a90e2;
        }
        
        .btn-primary:hover {
            background: #357abd;
        }
        
        .input-file {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        
        .status-info {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 20px;
        }
        
        .status-item {
            margin-bottom: 8px;
        }
        
        .status-label {
            font-weight: 500;
            color: #495057;
        }
        
        .status-value {
            color: #007bff;
        }
        
        .error-message {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
            border-radius: 4px;
            padding: 12px;
            margin-bottom: 15px;
        }
        
        .loading-message {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
            border-radius: 4px;
            padding: 12px;
            margin-bottom: 15px;
            text-align: center;
        }
        
        .feature-list {
            list-style: none;
            padding: 0;
        }
        
        .feature-item {
            padding: 10px 0;
            border-bottom: 1px solid #eee;
            display: flex;
            align-items: center;
        }
        
        .feature-item:last-child {
            border-bottom: none;
        }
        
        .feature-icon {
            width: 20px;
            height: 20px;
            background: #4a90e2;
            border-radius: 50%;
            margin-right: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 12px;
        }
        
        @media (max-width: 768px) {
            .demo-container {
                padding: 10px;
            }
            
            .demo-title {
                font-size: 2rem;
            }
            
            .controls-panel {
                flex-direction: column;
                gap: 10px;
            }
            
            .viewer-container {
                height: 400px;
            }
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="demo-container">
            <!-- 页面头部 -->
            <div class="demo-header">
                <h1 class="demo-title">OFD Viewer Vue 演示</h1>
                <p class="demo-subtitle">基于 liteofd 的 Vue OFD 文档查看器组件</p>
                <div class="demo-description">
                    <p>这是一个功能完整的 OFD 文档查看器 Vue 组件演示。支持文件上传、页面导航、缩放控制、内容搜索等功能。</p>
                </div>
            </div>
            
            <!-- 基础演示 -->
            <div class="demo-section">
                <div class="section-header">
                    <i class="feature-icon">📄</i>
                    基础 OFD 查看器演示
                </div>
                <div class="section-content">
                    <!-- 控制面板 -->
                    <div class="controls-panel">
                        <div class="control-group">
                            <label class="control-label">选择文件:</label>
                            <input 
                                type="file" 
                                accept=".ofd" 
                                @change="handleFileUpload"
                                class="input-file"
                            />
                        </div>
                        <div class="control-group">
                            <button @click="loadSampleFile" class="btn btn-primary">
                                加载示例文件
                            </button>
                        </div>
                    </div>
                    
                    <!-- 状态信息 -->
                    <div v-if="viewerStatus.fileName || viewerStatus.totalPages" class="status-info">
                        <div v-if="viewerStatus.fileName" class="status-item">
                            <span class="status-label">文件名:</span>
                            <span class="status-value">{{ viewerStatus.fileName }}</span>
                        </div>
                        <div v-if="viewerStatus.totalPages" class="status-item">
                            <span class="status-label">总页数:</span>
                            <span class="status-value">{{ viewerStatus.totalPages }}</span>
                        </div>
                        <div v-if="viewerStatus.currentPage" class="status-item">
                            <span class="status-label">当前页:</span>
                            <span class="status-value">{{ viewerStatus.currentPage }}</span>
                        </div>
                        <div v-if="viewerStatus.scale" class="status-item">
                            <span class="status-label">缩放比例:</span>
                            <span class="status-value">{{ Math.round(viewerStatus.scale * 100) }}%</span>
                        </div>
                    </div>
                    
                    <!-- 错误信息 -->
                    <div v-if="errorMessage" class="error-message">
                        {{ errorMessage }}
                    </div>
                    
                    <!-- 加载信息 -->
                    <div v-if="isLoading" class="loading-message">
                        正在加载 OFD 文档...
                    </div>
                    
                    <!-- OFD 查看器 -->
                    <div class="viewer-container">
                        <ofd-viewer
                            ref="ofdViewerRef"
                            :src="currentFile"
                            :show-toolbar="true"
                            :auto-load="true"
                            :scale="1"
                            @loaded="onDocumentLoaded"
                            @error="onDocumentError"
                            @page-change="onPageChange"
                            @scale-change="onScaleChange"
                            @signature-click="onSignatureClick"
                        />
                    </div>
                </div>
            </div>
            
            <!-- 功能特性 -->
            <div class="demo-section">
                <div class="section-header">
                    <i class="feature-icon">⚡</i>
                    功能特性
                </div>
                <div class="section-content">
                    <ul class="feature-list">
                        <li class="feature-item">
                            <div class="feature-icon">📁</div>
                            <div>支持本地 OFD 文件上传和预览</div>
                        </li>
                        <li class="feature-item">
                            <div class="feature-icon">🔍</div>
                            <div>内置缩放控制（放大、缩小、重置）</div>
                        </li>
                        <li class="feature-item">
                            <div class="feature-icon">📖</div>
                            <div>完整的页面导航功能（首页、上一页、下一页、末页）</div>
                        </li>
                        <li class="feature-item">
                            <div class="feature-icon">🎨</div>
                            <div>高质量的 OFD 文档渲染</div>
                        </li>
                        <li class="feature-item">
                            <div class="feature-icon">📱</div>
                            <div>响应式设计，支持移动端</div>
                        </li>
                        <li class="feature-item">
                            <div class="feature-icon">🔧</div>
                            <div>可自定义工具栏和样式</div>
                        </li>
                        <li class="feature-item">
                            <div class="feature-icon">⚡</div>
                            <div>轻量级，高性能</div>
                        </li>
                        <li class="feature-item">
                            <div class="feature-icon">🔐</div>
                            <div>支持数字签名识别和交互</div>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Vue 3 CDN -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>

    <!-- 主应用脚本 -->
    <script src="./main.js"></script>
</body>
</html>
