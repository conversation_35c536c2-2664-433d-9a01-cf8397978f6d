import { ToUnicodeMap } from "./to_unicode_map.js"

export const differences = () => {
  return [
		".notdef",
		"dotaccent",
		"fi",
		"fl",
		"fraction",
		"hungarumlaut",
		"Lslash",
		"lslash",
		"ogonek",
		"ring",
		".notdef",
		"breve",
		"minus",
		".notdef",
		"Zcaron",
		"zcaron",
		"caron",
		"dotlessi",
		"dotlessj",
		"ff",
		"ffi",
		"ffl",
		"notequal",
		"infinity",
		"lessequal",
		"greaterequal",
		"partialdiff",
		"summation",
		"product",
		"pi",
		"grave",
		"quotesingle",
		"space",
		"exclam",
		"quotedbl",
		"numbersign",
		"dollar",
		"percent",
		"ampersand",
		"quoteright",
		"parenleft",
		"parenright",
		"asterisk",
		"plus",
		"comma",
		"hyphen",
		"period",
		"slash",
		"zero",
		"one",
		"two",
		"three",
		"four",
		"five",
		"six",
		"seven",
		"eight",
		"nine",
		"colon",
		"semicolon",
		"less",
		"equal",
		"greater",
		"question",
		"at",
		"A",
		"B",
		"C",
		"D",
		"E",
		"F",
		"G",
		"H",
		"I",
		"J",
		"K",
		"L",
		"M",
		"N",
		"O",
		"P",
		"Q",
		"R",
		"S",
		"T",
		"U",
		"V",
		"W",
		"X",
		"Y",
		"Z",
		"bracketleft",
		"backslash",
		"bracketright",
		"asciicircum",
		"underscore",
		"quoteleft",
		"a",
		"b",
		"c",
		"d",
		"e",
		"f",
		"g",
		"h",
		"i",
		"j",
		"k",
		"l",
		"m",
		"n",
		"o",
		"p",
		"q",
		"r",
		"s",
		"t",
		"u",
		"v",
		"w",
		"x",
		"y",
		"z",
		"braceleft",
		"bar",
		"braceright",
		"asciitilde",
		".notdef",
		"Euro",
		"integral",
		"quotesinglbase",
		"florin",
		"quotedblbase",
		"ellipsis",
		"dagger",
		"daggerdbl",
		"circumflex",
		"perthousand",
		"Scaron",
		"guilsinglleft",
		"OE",
		"Omega",
		"radical",
		"approxequal",
		".notdef",
		null,
		null,
		"quotedblleft",
		"quotedblright",
		"bullet",
		"endash",
		"emdash",
		"tilde",
		"trademark",
		"scaron",
		"guilsinglright",
		"oe",
		"Delta",
		"lozenge",
		"Ydieresis",
		".notdef",
		"exclamdown",
		"cent",
		"sterling",
		"currency",
		"yen",
		"brokenbar",
		"section",
		"dieresis",
		"copyright",
		"ordfeminine",
		"guillemotleft",
		"logicalnot",
		"hyphen",
		"registered",
		"macron",
		"degree",
		"plusminus",
		"twosuperior",
		"threesuperior",
		"acute",
		"mu",
		"paragraph",
		"periodcentered",
		"cedilla",
		"onesuperior",
		"ordmasculine",
		"guillemotright",
		"onequarter",
		"onehalf",
		"threequarters",
		"questiondown",
		"Agrave",
		"Aacute",
		"Acircumflex",
		"Atilde",
		"Adieresis",
		"Aring",
		"AE",
		"Ccedilla",
		"Egrave",
		"Eacute",
		"Ecircumflex",
		"Edieresis",
		"Igrave",
		"Iacute",
		"Icircumflex",
		"Idieresis",
		"Eth",
		"Ntilde",
		"Ograve",
		"Oacute",
		"Ocircumflex",
		"Otilde",
		"Odieresis",
		"multiply",
		"Oslash",
		"Ugrave",
		"Uacute",
		"Ucircumflex",
		"Udieresis",
		"Yacute",
		"Thorn",
		"germandbls",
		"agrave",
		"aacute",
		"acircumflex",
		"atilde",
		"adieresis",
		"aring",
		"ae",
		"ccedilla",
		"egrave",
		"eacute",
		"ecircumflex",
		"edieresis",
		"igrave",
		"iacute",
		"icircumflex",
		"idieresis",
		"eth",
		"ntilde",
		"ograve",
		"oacute",
		"ocircumflex",
		"otilde",
		"odieresis",
		"divide",
		"oslash",
		"ugrave",
		"uacute",
		"ucircumflex",
		"udieresis",
		"yacute",
		"thorn",
		"ydieresis"
	]
}

export const defaultEncoding = [
	"",
	"",
	"",
	"",
	"",
	"",
	"",
	"",
	"",
	"",
	"",
	"",
	"",
	"",
	"",
	"",
	"",
	"",
	"",
	"",
	"",
	"",
	"",
	"",
	"",
	"",
	"",
	"",
	"",
	"",
	"",
	"",
	"space",
	"exclam",
	"quotedbl",
	"numbersign",
	"dollar",
	"percent",
	"ampersand",
	"quotesingle",
	"parenleft",
	"parenright",
	"asterisk",
	"plus",
	"comma",
	"hyphen",
	"period",
	"slash",
	"zero",
	"one",
	"two",
	"three",
	"four",
	"five",
	"six",
	"seven",
	"eight",
	"nine",
	"colon",
	"semicolon",
	"less",
	"equal",
	"greater",
	"question",
	"at",
	"A",
	"B",
	"C",
	"D",
	"E",
	"F",
	"G",
	"H",
	"I",
	"J",
	"K",
	"L",
	"M",
	"N",
	"O",
	"P",
	"Q",
	"R",
	"S",
	"T",
	"U",
	"V",
	"W",
	"X",
	"Y",
	"Z",
	"bracketleft",
	"backslash",
	"bracketright",
	"asciicircum",
	"underscore",
	"grave",
	"a",
	"b",
	"c",
	"d",
	"e",
	"f",
	"g",
	"h",
	"i",
	"j",
	"k",
	"l",
	"m",
	"n",
	"o",
	"p",
	"q",
	"r",
	"s",
	"t",
	"u",
	"v",
	"w",
	"x",
	"y",
	"z",
	"braceleft",
	"bar",
	"braceright",
	"asciitilde",
	"",
	"Adieresis",
	"Aring",
	"Ccedilla",
	"Eacute",
	"Ntilde",
	"Odieresis",
	"Udieresis",
	"aacute",
	"agrave",
	"acircumflex",
	"adieresis",
	"atilde",
	"aring",
	"ccedilla",
	"eacute",
	"egrave",
	"ecircumflex",
	"edieresis",
	"iacute",
	"igrave",
	"icircumflex",
	"idieresis",
	"ntilde",
	"oacute",
	"ograve",
	"ocircumflex",
	"odieresis",
	"otilde",
	"uacute",
	"ugrave",
	"ucircumflex",
	"udieresis",
	"dagger",
	"degree",
	"cent",
	"sterling",
	"section",
	"bullet",
	"paragraph",
	"germandbls",
	"registered",
	"copyright",
	"trademark",
	"acute",
	"dieresis",
	"notequal",
	"AE",
	"Oslash",
	"infinity",
	"plusminus",
	"lessequal",
	"greaterequal",
	"yen",
	"mu",
	"partialdiff",
	"summation",
	"product",
	"pi",
	"integral",
	"ordfeminine",
	"ordmasculine",
	"Omega",
	"ae",
	"oslash",
	"questiondown",
	"exclamdown",
	"logicalnot",
	"radical",
	"florin",
	"approxequal",
	"Delta",
	"guillemotleft",
	"guillemotright",
	"ellipsis",
	"space",
	"Agrave",
	"Atilde",
	"Otilde",
	"OE",
	"oe",
	"endash",
	"emdash",
	"quotedblleft",
	"quotedblright",
	"quoteleft",
	"quoteright",
	"divide",
	"lozenge",
	"ydieresis",
	"Ydieresis",
	"fraction",
	"currency",
	"guilsinglleft",
	"guilsinglright",
	"fi",
	"fl",
	"daggerdbl",
	"periodcentered",
	"quotesinglbase",
	"quotedblbase",
	"perthousand",
	"Acircumflex",
	"Ecircumflex",
	"Aacute",
	"Edieresis",
	"Egrave",
	"Iacute",
	"Icircumflex",
	"Idieresis",
	"Igrave",
	"Oacute",
	"Ocircumflex",
	"apple",
	"Ograve",
	"Uacute",
	"Ucircumflex",
	"Ugrave",
	"dotlessi",
	"circumflex",
	"tilde",
	"macron",
	"breve",
	"dotaccent",
	"ring",
	"cedilla",
	"hungarumlaut",
	"ogonek",
	"caron"
]

export const glyphsWidths = [
	null,
	null,
	null,
	null,
	null,
	null,
	null,
	null,
	null,
	null,
	null,
	null,
	null,
	null,
	null,
	null,
	null,
	null,
	null,
	null,
	null,
	null,
	null,
	null,
	null,
	null,
	null,
	null,
	null,
	null,
	null,
	null,
	null,
	null,
	null,
	null,
	null,
	null,
	null,
	null,
	null,
	null,
	null,
	null,
	null,
	333,
	250,
	0,
	500,
	500,
	500,
	500,
	500,
	500,
	500,
	500,
	500,
	500,
	333,
	0,
	0,
	570,
	0,
	0,
	0,
	722,
	667,
	722,
	722,
	667,
	611,
	778,
	0,
	389,
	500,
	0,
	667,
	944,
	722,
	778,
	611,
	0,
	722,
	556,
	667,
	0,
	722,
	1000,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	500,
	556,
	444,
	556,
	444,
	333,
	500,
	556,
	278,
	333,
	556,
	278,
	833,
	556,
	500,
	556,
	0,
	444,
	389,
	333,
	556,
	500,
	722,
	500,
	500,
	444
]

export const toUnicodeMap = new ToUnicodeMap([
	null,
	"˙",
	"ﬁ",
	"ﬂ",
	"⁄",
	"˝",
	"Ł",
	"ł",
	"˛",
	"˚",
	null,
	"˘",
	"−",
	null,
	"Ž",
	"ž",
	"ˇ",
	"ı",
	"",
	"ﬀ",
	"ﬃ",
	"ﬄ",
	"≠",
	"∞",
	"≤",
	"≥",
	"∂",
	"∑",
	"∏",
	"π",
	"`",
	"'",
	" ",
	"!",
	"\"",
	"#",
	"$",
	"%",
	"&",
	"’",
	"(",
	")",
	"*",
	"+",
	",",
	"-",
	".",
	"/",
	"0",
	"1",
	"2",
	"3",
	"4",
	"5",
	"6",
	"7",
	"8",
	"9",
	":",
	";",
	"<",
	"=",
	">",
	"?",
	"@",
	"A",
	"B",
	"C",
	"D",
	"E",
	"F",
	"G",
	"H",
	"I",
	"J",
	"K",
	"L",
	"M",
	"N",
	"O",
	"P",
	"Q",
	"R",
	"S",
	"T",
	"U",
	"V",
	"W",
	"X",
	"Y",
	"Z",
	"[",
	"\\",
	"]",
	"^",
	"_",
	"‘",
	"a",
	"b",
	"c",
	"d",
	"e",
	"f",
	"g",
	"h",
	"i",
	"j",
	"k",
	"l",
	"m",
	"n",
	"o",
	"p",
	"q",
	"r",
	"s",
	"t",
	"u",
	"v",
	"w",
	"x",
	"y",
	"z",
	"{",
	"|",
	"}",
	"~",
	null,
	"€",
	"∫",
	"‚",
	"ƒ",
	"„",
	"…",
	"†",
	"‡",
	"ˆ",
	"‰",
	"Š",
	"‹",
	"Œ",
	"Ω",
	"√",
	"≈",
	"ê",
	"ë",
	"í",
	"“",
	"”",
	"•",
	"–",
	"—",
	"˜",
	"™",
	"š",
	"›",
	"œ",
	"∆",
	"◊",
	"Ÿ",
	"†",
	"¡",
	"¢",
	"£",
	"¤",
	"¥",
	"¦",
	"§",
	"¨",
	"©",
	"ª",
	"«",
	"¬",
	"-",
	"®",
	"¯",
	"°",
	"±",
	"²",
	"³",
	"´",
	"µ",
	"¶",
	"·",
	"¸",
	"¹",
	"º",
	"»",
	"¼",
	"½",
	"¾",
	"¿",
	"À",
	"Á",
	"Â",
	"Ã",
	"Ä",
	"Å",
	"Æ",
	"Ç",
	"È",
	"É",
	"Ê",
	"Ë",
	"Ì",
	"Í",
	"Î",
	"Ï",
	"Ð",
	"Ñ",
	"Ò",
	"Ó",
	"Ô",
	"Õ",
	"Ö",
	"×",
	"Ø",
	"Ù",
	"Ú",
	"Û",
	"Ü",
	"Ý",
	"Þ",
	"ß",
	"à",
	"á",
	"â",
	"ã",
	"ä",
	"å",
	"æ",
	"ç",
	"è",
	"é",
	"ê",
	"ë",
	"ì",
	"í",
	"î",
	"ï",
	"ð",
	"ñ",
	"ò",
	"ó",
	"ô",
	"õ",
	"ö",
	"÷",
	"ø",
	"ù",
	"ú",
	"û",
	"ü",
	"ý",
	"þ",
	"ÿ"
])
