<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SVG 动态渲染器</title>
    <style>
        #svgContainer {
            border: 1px solid #ccc;
            margin: 20px;
            width: 500px;
            height: 500px;
            position: relative;
        }
        #svgInput {
            width: 100%;
            height: 200px;
            margin-bottom: 10px;
        }
        .button-group {
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <textarea id="svgInput" placeholder="在这里输入 SVG 代码..."></textarea>
    <div class="button-group">
        <button id="renderButton">渲染 SVG</button>
        <button id="formatButton">格式化 XML</button>
    </div>
    <div id="svgContainer"></div>

    <script src="svgRender.js"></script>
</body>
</html>
