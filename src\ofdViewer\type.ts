import { type OfdDocument } from '../liteofd/ofdDocument'

export interface OfdViewerProps {
  src?: string | File | ArrayBuffer
  showToolbar?: boolean
  pageWrapStyle?: string
  pageIndexes?: number[]
  autoLoad?: boolean
  scale?: number
}

export interface OfdViewerEmits {
  loaded: [document: OfdDocument]
  error: [error: Error]
  pageChange: [page: number]
  scaleChange: [scale: number]
  signatureClick: [data: { nodeData: any; sealObject: any }]
}

export interface OfdViewerExpose {
  loadFile: (file: string | File | ArrayBuffer) => Promise<void>
  goToPage: (page: number) => void
  firstPage: () => void
  prevPage: () => void
  nextPage: () => void
  lastPage: () => void
  zoomIn: () => void
  zoomOut: () => void
  resetZoom: () => void
  setZoom: (scale: number) => void
  search: (keyword: string) => void
  getContent: (page?: number) => string
  getCurrentPage: () => number
  getTotalPages: () => number
  getScale: () => number
  getDocument: () => OfdDocument | undefined
}