<template>
  <div class="ofd-demo-app">
    <!-- 页面头部 -->
    <header class="demo-header">
      <h1>OFD Viewer 集成演示</h1>
      <p>基于 liteofd 的 Vue OFD 文档查看器</p>
    </header>

    <!-- 主要内容区域 -->
    <main class="demo-main">
      <!-- 控制面板 -->
      <aside class="control-sidebar">
        <div class="control-section">
          <h3>文件操作</h3>
          <div class="control-group">
            <label>选择 OFD 文件:</label>
            <input 
              type="file" 
              accept=".ofd" 
              @change="handleFileSelect"
              class="file-input"
            />
          </div>
          
          <div class="control-group">
            <button 
              @click="loadSampleFile" 
              class="btn btn-primary"
              :disabled="loading"
            >
              {{ loading ? '加载中...' : '加载示例文件' }}
            </button>
          </div>
        </div>

        <div class="control-section">
          <h3>查看器设置</h3>
          <div class="control-group">
            <label>
              <input 
                type="checkbox" 
                v-model="viewerSettings.showToolbar"
              />
              显示工具栏
            </label>
          </div>
          
          <div class="control-group">
            <label>
              <input 
                type="checkbox" 
                v-model="viewerSettings.autoLoad"
              />
              自动加载文件
            </label>
          </div>
          
          <div class="control-group">
            <label>初始缩放:</label>
            <select v-model="viewerSettings.initialScale">
              <option value="0.5">50%</option>
              <option value="0.75">75%</option>
              <option value="1">100%</option>
              <option value="1.25">125%</option>
              <option value="1.5">150%</option>
            </select>
          </div>
        </div>

        <div class="control-section">
          <h3>文档信息</h3>
          <div class="info-item">
            <span class="info-label">文件名:</span>
            <span class="info-value">{{ documentInfo.fileName || '未选择' }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">页数:</span>
            <span class="info-value">{{ documentInfo.totalPages || 0 }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">当前页:</span>
            <span class="info-value">{{ documentInfo.currentPage || 0 }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">缩放:</span>
            <span class="info-value">{{ Math.round((documentInfo.scale || 1) * 100) }}%</span>
          </div>
        </div>

        <div class="control-section">
          <h3>操作控制</h3>
          <div class="control-group">
            <button 
              @click="goToFirstPage" 
              :disabled="!canNavigate || !canGoPrev"
              class="btn btn-sm"
            >
              首页
            </button>
            <button 
              @click="goToPrevPage" 
              :disabled="!canNavigate || !canGoPrev"
              class="btn btn-sm"
            >
              上一页
            </button>
            <button 
              @click="goToNextPage" 
              :disabled="!canNavigate || !canGoNext"
              class="btn btn-sm"
            >
              下一页
            </button>
            <button 
              @click="goToLastPage" 
              :disabled="!canNavigate || !canGoNext"
              class="btn btn-sm"
            >
              末页
            </button>
          </div>
          
          <div class="control-group">
            <button 
              @click="zoomIn" 
              :disabled="!canNavigate"
              class="btn btn-sm"
            >
              放大
            </button>
            <button 
              @click="zoomOut" 
              :disabled="!canNavigate"
              class="btn btn-sm"
            >
              缩小
            </button>
            <button 
              @click="resetZoom" 
              :disabled="!canNavigate"
              class="btn btn-sm"
            >
              重置
            </button>
          </div>
        </div>
      </aside>

      <!-- 查看器区域 -->
      <section class="viewer-area">
        <div class="viewer-container">
          <!-- 错误提示 -->
          <div v-if="error" class="error-message">
            <h4>加载错误</h4>
            <p>{{ error }}</p>
            <button @click="clearError" class="btn btn-sm">清除错误</button>
          </div>

          <!-- OFD 查看器组件 -->
          <OfdViewer
            ref="ofdViewerRef"
            :src="currentFile"
            :show-toolbar="viewerSettings.showToolbar"
            :auto-load="viewerSettings.autoLoad"
            :scale="viewerSettings.initialScale"
            @loaded="onDocumentLoaded"
            @error="onDocumentError"
            @page-change="onPageChange"
            @scale-change="onScaleChange"
            @signature-click="onSignatureClick"
            class="ofd-viewer-component"
          />
        </div>
      </section>
    </main>

    <!-- 状态栏 -->
    <footer class="demo-footer">
      <div class="status-info">
        <span v-if="loading" class="status-loading">⏳ 加载中...</span>
        <span v-else-if="error" class="status-error">❌ 加载失败</span>
        <span v-else-if="documentInfo.fileName" class="status-success">
          ✅ {{ documentInfo.fileName }} - {{ documentInfo.currentPage }}/{{ documentInfo.totalPages }}
        </span>
        <span v-else class="status-idle">📄 请选择 OFD 文件</span>
      </div>
      
      <div class="action-buttons">
        <button 
          @click="getDocumentContent" 
          :disabled="!canNavigate"
          class="btn btn-sm"
        >
          获取内容
        </button>
        <button 
          @click="exportDocument" 
          :disabled="!canNavigate"
          class="btn btn-sm"
        >
          导出文档
        </button>
      </div>
    </footer>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import OfdViewer from '../../src/ofdViewer/ofdViewer.vue'
import type { OfdDocument } from '../../src/liteofd/ofdDocument'

// 响应式数据
const ofdViewerRef = ref<InstanceType<typeof OfdViewer>>()
const currentFile = ref<string | File | ArrayBuffer | null>(null)
const loading = ref(false)
const error = ref('')

const viewerSettings = reactive({
  showToolbar: true,
  autoLoad: true,
  initialScale: 1
})

const documentInfo = reactive({
  fileName: '',
  totalPages: 0,
  currentPage: 0,
  scale: 1
})

// 计算属性
const canNavigate = computed(() => documentInfo.totalPages > 0)
const canGoPrev = computed(() => documentInfo.currentPage > 1)
const canGoNext = computed(() => documentInfo.currentPage < documentInfo.totalPages)

// 文件处理方法
const handleFileSelect = (event: Event) => {
  const target = event.target as HTMLInputElement
  const file = target.files?.[0]
  
  if (file) {
    if (!file.name.toLowerCase().endsWith('.ofd')) {
      error.value = '请选择 .ofd 格式的文件'
      return
    }
    
    clearError()
    documentInfo.fileName = file.name
    currentFile.value = file
    loading.value = true
  }
}

const loadSampleFile = async () => {
  try {
    loading.value = true
    clearError()
    
    // 这里应该加载实际的示例文件
    // const response = await fetch('/path/to/sample.ofd')
    // const arrayBuffer = await response.arrayBuffer()
    // currentFile.value = arrayBuffer
    // documentInfo.fileName = 'sample.ofd'
    
    // 临时提示
    throw new Error('请提供示例 OFD 文件路径')
    
  } catch (err) {
    error.value = err instanceof Error ? err.message : '加载示例文件失败'
    loading.value = false
  }
}

const clearError = () => {
  error.value = ''
}

// OFD Viewer 事件处理
const onDocumentLoaded = (document: OfdDocument) => {
  loading.value = false
  
  if (ofdViewerRef.value) {
    documentInfo.totalPages = ofdViewerRef.value.getTotalPages()
    documentInfo.currentPage = ofdViewerRef.value.getCurrentPage()
    documentInfo.scale = ofdViewerRef.value.getScale()
  }
  
  console.log('文档加载成功:', document)
}

const onDocumentError = (err: Error) => {
  loading.value = false
  error.value = err.message
  console.error('文档加载失败:', err)
}

const onPageChange = (page: number) => {
  documentInfo.currentPage = page
  console.log('页面变化:', page)
}

const onScaleChange = (scale: number) => {
  documentInfo.scale = scale
  console.log('缩放变化:', scale)
}

const onSignatureClick = (data: any) => {
  console.log('签名点击事件:', data)
  alert('检测到数字签名，详情请查看控制台')
}

// 控制方法
const goToFirstPage = () => ofdViewerRef.value?.firstPage()
const goToPrevPage = () => ofdViewerRef.value?.prevPage()
const goToNextPage = () => ofdViewerRef.value?.nextPage()
const goToLastPage = () => ofdViewerRef.value?.lastPage()

const zoomIn = () => ofdViewerRef.value?.zoomIn()
const zoomOut = () => ofdViewerRef.value?.zoomOut()
const resetZoom = () => ofdViewerRef.value?.resetZoom()

const getDocumentContent = () => {
  if (ofdViewerRef.value) {
    const content = ofdViewerRef.value.getContent()
    console.log('文档内容:', content)
    alert(`文档内容已输出到控制台 (${content.length} 字符)`)
  }
}

const exportDocument = () => {
  if (ofdViewerRef.value) {
    const document = ofdViewerRef.value.getDocument()
    console.log('文档对象:', document)
    alert('文档对象已输出到控制台')
  }
}

// 生命周期
onMounted(() => {
  console.log('OFD Viewer 集成演示已加载')
})
</script>

<style scoped>
.ofd-demo-app {
  height: 100vh;
  display: flex;
  flex-direction: column;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.demo-header {
  background: #4a90e2;
  color: white;
  padding: 20px;
  text-align: center;
}

.demo-header h1 {
  margin: 0 0 8px 0;
  font-size: 1.8rem;
}

.demo-header p {
  margin: 0;
  opacity: 0.9;
}

.demo-main {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.control-sidebar {
  width: 300px;
  background: #f8f9fa;
  border-right: 1px solid #dee2e6;
  overflow-y: auto;
  padding: 20px;
}

.control-section {
  margin-bottom: 25px;
  padding-bottom: 20px;
  border-bottom: 1px solid #e9ecef;
}

.control-section:last-child {
  border-bottom: none;
}

.control-section h3 {
  margin: 0 0 15px 0;
  font-size: 1.1rem;
  color: #495057;
}

.control-group {
  margin-bottom: 15px;
}

.control-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  color: #495057;
}

.file-input, select {
  width: 100%;
  padding: 8px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 14px;
}

.btn {
  padding: 8px 16px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  background: white;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s;
  margin-right: 8px;
  margin-bottom: 8px;
}

.btn:hover:not(:disabled) {
  background: #e9ecef;
  border-color: #adb5bd;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-primary {
  background: #4a90e2;
  color: white;
  border-color: #4a90e2;
}

.btn-primary:hover:not(:disabled) {
  background: #357abd;
}

.btn-sm {
  padding: 6px 12px;
  font-size: 12px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 14px;
}

.info-label {
  color: #6c757d;
}

.info-value {
  font-weight: 500;
  color: #495057;
}

.viewer-area {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.viewer-container {
  flex: 1;
  position: relative;
}

.ofd-viewer-component {
  height: 100%;
}

.error-message {
  position: absolute;
  top: 20px;
  left: 20px;
  right: 20px;
  background: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
  border-radius: 4px;
  padding: 15px;
  z-index: 10;
}

.error-message h4 {
  margin: 0 0 8px 0;
}

.error-message p {
  margin: 0 0 10px 0;
}

.demo-footer {
  background: #f8f9fa;
  border-top: 1px solid #dee2e6;
  padding: 15px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.status-info {
  font-size: 14px;
}

.status-loading { color: #ffc107; }
.status-error { color: #dc3545; }
.status-success { color: #28a745; }
.status-idle { color: #6c757d; }

.action-buttons {
  display: flex;
  gap: 10px;
}

@media (max-width: 768px) {
  .demo-main {
    flex-direction: column;
  }
  
  .control-sidebar {
    width: 100%;
    height: auto;
    max-height: 300px;
  }
  
  .demo-footer {
    flex-direction: column;
    gap: 10px;
    text-align: center;
  }
}
</style>
