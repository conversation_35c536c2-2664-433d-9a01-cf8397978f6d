// ASN.1 RFC definitions matcher
// Copyright (c) 2023-2024 <PERSON><PERSON> <<EMAIL>>

// Permission to use, copy, modify, and/or distribute this software for any
// purpose with or without fee is hereby granted, provided that the above
// copyright notice and this permission notice appear in all copies.
// 
// THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES
// WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF
// MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR
// ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES
// WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN
// ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF
// OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.

import { rfcdef } from './rfcdef.js';

function translate(def, tn, stats) {
    if (def?.type == 'tag' && !def.explicit)
        // def.type = def.content[0].type;
        def = def.content[0].type;
    if (def?.definedBy)
        try {
            // hope current OIDs contain the type name (will need to parse from RFC itself)
            def = Defs.searchType(firstUpper(stats.defs[def.definedBy][1]));
        } catch (e) { /*ignore*/ }
    while (def?.type == 'defined' || def?.type?.type == 'defined') {
        const name = def?.type?.type ? def.type.name : def.name;
        def = Object.assign({}, def);
        def.type = Defs.searchType(name).type;
    }
    if (def?.type?.name == 'CHOICE') {
        for (let c of def.type.content) {
            if (tn != c.type.name && tn != c.name)
                c = translate(c);
            if (tn == c.type.name || tn == c.name) {
                def = Object.assign({}, def);
                def.type = c.type.name ? c.type : c;
                break;
            }
        }
    }
    const id = def?.id;
    if (id)
        def = Object.assign({}, def, { id });
    return def ?? { type: {} };
}

function firstUpper(s) {
    return s[0].toUpperCase() + s.slice(1);
}

export class Defs {

    static moduleAndType(mod, name) {
        return Object.assign({ module: { oid: mod.oid, name: mod.name, source: mod.source } }, mod.types[name]);
    }

    static searchType(name) {
        for (const mod of Object.values(rfcdef))
            if (name in mod.types) {
                // console.log(name + ' found in ' + r.name);
                // return r.types[name];
                return Defs.moduleAndType(mod, name);
            }
        throw new Error('Type not found: ' + name);
    }

    static match(value, def, stats = { total: 0, recognized: 0, defs: {} }) {
        value.def = {};
        let tn = value.typeName().replaceAll('_', ' ');
        def = translate(def, tn, stats);
        ++stats.total;
        if (def?.type) {
            // if (def.id || def.name) ++stats.recognized;
            if (tn == def.type.name || tn == def.name || def.name == 'ANY')
                ++stats.recognized;
            else if (def.name)
                def = Object.assign({ mismatch: 1 }, def);
            value.def = def;
        }
        if (value.sub !== null) {
            if (def?.type?.type)
                def = def.type;
            let j = def?.content ? 0 : -1;
            for (const subval of value.sub) {
                let type;
                if (j >= 0) {
                    if (def.typeOf)
                        type = def.content[0];
                    else {
                        let tn = subval.typeName().replaceAll('_', ' ');
                        for (;;) {
                            type = def.content[j++];
                            if (!type || typeof type != 'object') break;
                            if (type?.type?.type)
                                type = type.type;
                            if (type.type == 'defined') {
                                let t2 = translate(type, tn);
                                if (t2.type.name == tn) break; // exact match
                                if (t2.type.name == 'ANY') break; // good enough
                            }
                            if (type.name == tn) break; // exact match
                            if (type.name == 'ANY') break; // good enough
                            if (!('optional' in type || 'default' in type)) break;
                        }
                        if (type?.type == 'builtin' || type?.type == 'defined') {
                            let v = subval.content();
                            if (typeof v == 'string')
                                v = v.split(/\n/);
                            stats.defs[type.id] = v;
                        } else if (type?.definedBy && stats.defs?.[type.definedBy]?.[1]) { // hope current OIDs contain the type name (will need to parse from RFC itself)
                            try {
                                type = Defs.searchType(firstUpper(stats.defs[type.definedBy][1]));
                            } catch (e) { /*ignore*/ }
                        }
                    }
                }
                Defs.match(subval, type, stats);
            }
        }
        return stats;
    }

}

Defs.RFC = rfcdef;

Defs.commonTypes = [
    [ 'X.509 certificate', '*******.*******.18', 'Certificate' ], 
    [ 'X.509 public key info', '*******.*******.18', 'SubjectPublicKeyInfo' ],
    [ 'CMS / PKCS#7 envelope', '1.2.840.113549.********.14', 'ContentInfo' ],
    [ 'PKCS#1 RSA private key', '1.2.840.113549.*******', 'RSAPrivateKey' ],
    [ 'PKCS#8 encrypted private key', '1.2.840.113549.*******', 'EncryptedPrivateKeyInfo' ],
    [ 'PKCS#8 private key', '1.2.840.113549.*******', 'PrivateKeyInfo' ],
    [ 'PKCS#10 certification request', '1.2.840.113549.********', 'CertificationRequest' ],
    [ 'CMP PKI Message', '*******.*******.16', 'PKIMessage' ],
].map(arr => ({ description: arr[0], ...Defs.moduleAndType(rfcdef[arr[1]], arr[2]) }));
