<template>
  <div class="ofd-viewer" ref="viewerContainer">
    <!-- 工具栏 -->
    <div v-if="showToolbar" class="ofd-toolbar">
      <div class="toolbar-group">
        <input 
          ref="fileInput" 
          type="file" 
          accept=".ofd" 
          @change="handleFileChange" 
          style="display: none"
        />
        <button @click="selectFile" class="toolbar-btn">选择文件</button>
        <span v-if="fileName" class="file-name">{{ fileName }}</span>
      </div>
      
      <div class="toolbar-group">
        <button @click="zoomOut" :disabled="!isLoaded" class="toolbar-btn">缩小</button>
        <button @click="zoomIn" :disabled="!isLoaded" class="toolbar-btn">放大</button>
        <button @click="resetZoom" :disabled="!isLoaded" class="toolbar-btn">重置</button>
      </div>
      
      <div class="toolbar-group">
        <button @click="firstPage" :disabled="!canGoPrev" class="toolbar-btn">首页</button>
        <button @click="prevPage" :disabled="!canGoPrev" class="toolbar-btn">上一页</button>
        <span class="page-info">{{ currentPage }} / {{ totalPages }}</span>
        <button @click="nextPage" :disabled="!canGoNext" class="toolbar-btn">下一页</button>
        <button @click="lastPage" :disabled="!canGoNext" class="toolbar-btn">末页</button>
      </div>
    </div>
    
    <!-- 内容区域 -->
    <div class="ofd-content" ref="contentContainer">
      <div v-if="loading" class="loading">加载中...</div>
      <div v-else-if="error" class="error">{{ error }}</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import LiteOfd from '../liteofd/liteOfd'
import type { OfdDocument } from '../liteofd/ofdDocument'

// Props定义
interface Props {
  src?: string | File | ArrayBuffer  // OFD文件源
  showToolbar?: boolean              // 是否显示工具栏
  pageWrapStyle?: string            // 页面包装样式
  pageIndexes?: number[]            // 指定渲染的页面
  autoLoad?: boolean                // 是否自动加载
  scale?: number                    // 初始缩放比例
}

const props = withDefaults(defineProps<Props>(), {
  showToolbar: true,
  autoLoad: true,
  scale: 1
})

// Emits定义
interface Emits {
  (e: 'loaded', document: OfdDocument): void
  (e: 'error', error: Error): void
  (e: 'pageChange', page: number): void
  (e: 'scaleChange', scale: number): void
  (e: 'signatureClick', data: any): void
}

const emit = defineEmits<Emits>()

// 响应式状态
const viewerContainer = ref<HTMLDivElement>()
const contentContainer = ref<HTMLDivElement>()
const fileInput = ref<HTMLInputElement>()

const liteOfd = ref<LiteOfd>()
const ofdDocument = ref<OfdDocument>()
const loading = ref(false)
const error = ref<string>('')
const fileName = ref<string>('')

const currentPage = ref(1)
const totalPages = ref(0)
const currentScale = ref(props.scale)

// 计算属性
const isLoaded = computed(() => !!ofdDocument.value)
const canGoPrev = computed(() => isLoaded.value && currentPage.value > 1)
const canGoNext = computed(() => isLoaded.value && currentPage.value < totalPages.value)

// 核心方法实现
const initLiteOfd = () => {
  liteOfd.value = new LiteOfd()
  setupEventListeners()
}

const setupEventListeners = () => {
  // 监听页面变化事件
  window.addEventListener('ofdPageChange', handlePageChange)
  
  // 监听签名点击事件
  contentContainer.value?.addEventListener('signature-element-click', handleSignatureClick)
}

const handlePageChange = (event: any) => {
  if (liteOfd.value) {
    currentPage.value = liteOfd.value.currentPage
    emit('pageChange', currentPage.value)
  }
}

const handleSignatureClick = (event: any) => {
  const { nodeData, sealObject } = event.detail
  emit('signatureClick', { nodeData, sealObject })
}

// 文件处理方法
const loadOfdFile = async (file: string | File | ArrayBuffer) => {
  if (!liteOfd.value) return
  
  loading.value = true
  error.value = ''
  
  try {
    ofdDocument.value = await liteOfd.value.parse(file)
    totalPages.value = liteOfd.value.totalPages
    currentPage.value = 1
    
    // 渲染文档
    const container = liteOfd.value.render(
      contentContainer.value, 
      props.pageWrapStyle, 
      props.pageIndexes
    )
    
    if (contentContainer.value) {
      contentContainer.value.innerHTML = ''
      contentContainer.value.appendChild(container)
    }
    
    emit('loaded', ofdDocument.value)
  } catch (err) {
    error.value = err instanceof Error ? err.message : '加载失败'
    emit('error', err as Error)
  } finally {
    loading.value = false
  }
}

// 导航方法
const firstPage = () => {
  liteOfd.value?.goToPage(1)
}

const prevPage = () => {
  liteOfd.value?.prevPage()
}

const nextPage = () => {
  liteOfd.value?.nextPage()
}

const lastPage = () => {
  liteOfd.value?.goToPage(totalPages.value)
}

const goToPage = (page: number) => {
  liteOfd.value?.goToPage(page)
}

// 缩放方法
const zoomIn = () => {
  liteOfd.value?.zoomIn()
  currentScale.value = liteOfd.value?.currentScale || currentScale.value
  emit('scaleChange', currentScale.value)
}

const zoomOut = () => {
  liteOfd.value?.zoomOut()
  currentScale.value = liteOfd.value?.currentScale || currentScale.value
  emit('scaleChange', currentScale.value)
}

const resetZoom = () => {
  liteOfd.value?.resetZoom()
  currentScale.value = 1
  emit('scaleChange', currentScale.value)
}

const setZoom = (scale: number) => {
  liteOfd.value?.zoom(scale)
  currentScale.value = scale
  emit('scaleChange', currentScale.value)
}

// 文件选择
const selectFile = () => {
  fileInput.value?.click()
}

const handleFileChange = (event: Event) => {
  const target = event.target as HTMLInputElement
  const file = target.files?.[0]
  if (file) {
    fileName.value = file.name
    loadOfdFile(file)
  }
}

// 搜索功能
const search = (keyword: string) => {
  liteOfd.value?.search(keyword)
}

// 获取内容
const getContent = (page?: number) => {
  return liteOfd.value?.getContent(page) || ''
}

// 生命周期处理
onMounted(() => {
  initLiteOfd()
  
  // 如果有初始文件源且自动加载
  if (props.src && props.autoLoad) {
    loadOfdFile(props.src)
  }
})

onUnmounted(() => {
  // 清理事件监听器
  window.removeEventListener('ofdPageChange', handlePageChange)
  contentContainer.value?.removeEventListener('signature-element-click', handleSignatureClick)
  
  // 清理配置UI
  liteOfd.value?.destroyConfigUI()
})

// 监听props变化
watch(() => props.src, (newSrc) => {
  if (newSrc && props.autoLoad) {
    loadOfdFile(newSrc)
  }
})

watch(() => props.scale, (newScale) => {
  if (newScale !== currentScale.value) {
    setZoom(newScale)
  }
})

// 暴露方法给父组件
defineExpose({
  loadFile: loadOfdFile,
  goToPage,
  firstPage,
  prevPage,
  nextPage,
  lastPage,
  zoomIn,
  zoomOut,
  resetZoom,
  setZoom,
  search,
  getContent,
  getCurrentPage: () => currentPage.value,
  getTotalPages: () => totalPages.value,
  getScale: () => currentScale.value,
  getDocument: () => ofdDocument.value
})
</script>