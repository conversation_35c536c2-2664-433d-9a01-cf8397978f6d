<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LiteOFD 开发演示</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .container {
            background: white;
            border-radius: 12px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 40px;
            max-width: 800px;
            width: 90%;
            text-align: center;
        }
        
        .logo {
            width: 80px;
            height: 80px;
            background: #4a90e2;
            border-radius: 50%;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            color: white;
        }
        
        h1 {
            color: #333;
            margin-bottom: 10px;
            font-size: 2.5rem;
        }
        
        .subtitle {
            color: #666;
            margin-bottom: 30px;
            font-size: 1.2rem;
        }
        
        .demo-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }
        
        .demo-card {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 25px;
            text-align: left;
            transition: transform 0.2s, box-shadow 0.2s;
        }
        
        .demo-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }
        
        .demo-card h3 {
            color: #333;
            margin-bottom: 10px;
            font-size: 1.3rem;
        }
        
        .demo-card p {
            color: #666;
            margin-bottom: 15px;
            line-height: 1.5;
        }
        
        .demo-link {
            display: inline-block;
            background: #4a90e2;
            color: white;
            padding: 10px 20px;
            border-radius: 5px;
            text-decoration: none;
            transition: background 0.2s;
        }
        
        .demo-link:hover {
            background: #357abd;
        }
        
        .features {
            margin-top: 40px;
            text-align: left;
        }
        
        .features h3 {
            color: #333;
            margin-bottom: 15px;
            text-align: center;
        }
        
        .feature-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
        }
        
        .feature-item {
            display: flex;
            align-items: center;
            padding: 10px;
            background: #f0f8ff;
            border-radius: 5px;
        }
        
        .feature-icon {
            width: 30px;
            height: 30px;
            background: #4a90e2;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            font-size: 14px;
            color: white;
        }
        
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #eee;
            color: #666;
            font-size: 14px;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 20px;
            }
            
            h1 {
                font-size: 2rem;
            }
            
            .demo-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">📄</div>
        <h1>LiteOFD</h1>
        <p class="subtitle">轻量级 OFD 文档解析和渲染库</p>
        
        <div class="demo-grid">
            <div class="demo-card">
                <h3>🎯 基础演示</h3>
                <p>简洁的 OFD Viewer 组件演示，包含基本的文件上传和查看功能，适合快速了解组件特性。</p>
                <a href="/example/vue-demo/index.html" class="demo-link">查看基础演示</a>
            </div>
            
            <div class="demo-card">
                <h3>⚡ 完整演示</h3>
                <p>功能完整的演示应用，包含详细的控制面板、实时状态监控、事件日志记录等高级功能。</p>
                <a href="/example/vue-demo/vue-app.html" class="demo-link">查看完整演示</a>
            </div>
            
            <div class="demo-card">
                <h3>🔧 原生演示</h3>
                <p>基于原生 JavaScript 的 OFD 查看器演示，展示 liteofd 核心库的使用方法。</p>
                <a href="/index.html" class="demo-link">查看原生演示</a>
            </div>
            
            <div class="demo-card">
                <h3>🛠️ 工具页面</h3>
                <p>开发者工具页面，包含各种测试和调试功能，适合开发和测试使用。</p>
                <a href="/tools.html" class="demo-link">查看工具页面</a>
            </div>
        </div>
        
        <div class="features">
            <h3>✨ 主要特性</h3>
            <div class="feature-list">
                <div class="feature-item">
                    <div class="feature-icon">📁</div>
                    <span>支持 OFD 文件解析</span>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">🎨</div>
                    <span>高质量文档渲染</span>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">🔍</div>
                    <span>缩放和导航控制</span>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">📱</div>
                    <span>响应式设计</span>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">⚡</div>
                    <span>轻量级高性能</span>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">🔐</div>
                    <span>数字签名支持</span>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">🎛️</div>
                    <span>Vue 组件集成</span>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">🔧</div>
                    <span>可自定义配置</span>
                </div>
            </div>
        </div>
        
        <div class="footer">
            <p>LiteOFD - 基于 TypeScript 开发的现代化 OFD 文档处理库</p>
            <p>支持 Vue 3 组件集成，提供完整的开发演示和文档</p>
        </div>
    </div>
</body>
</html>
