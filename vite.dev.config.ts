import { defineConfig } from 'vite'
import { resolve } from 'path'

// 专门用于开发模式的 Vite 配置
export default defineConfig({
  // 基本配置
  base: './',
  root: '.',
  
  // 开发服务器配置
  server: {
    host: '0.0.0.0',
    port: 3000,
    open: '/dev-index.html', // 自动打开开发首页
    cors: true,
    fs: {
      // 允许访问项目根目录
      allow: ['..', '.']
    },
    // 中间件配置，处理路由
    middlewareMode: false,
    hmr: {
      port: 3001
    }
  },

  // 解析配置
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
      '~': resolve(__dirname, '.')
    },
    extensions: ['.js', '.ts', '.vue', '.json', '.html']
  },

  // 开发模式下不需要构建配置
  build: {
    // 开发模式下的简单构建配置
    outDir: 'dev-dist',
    sourcemap: true,
    minify: false
  },

  // CSS 配置
  css: {
    devSourcemap: true
  },

  // 公共目录
  publicDir: 'public',

  // 插件 - 开发模式下保持简单
  plugins: [],

  // 优化配置
  optimizeDeps: {
    // 预构建依赖，避免开发时的依赖问题
    include: [
      'vue'
    ],
    // 排除有问题的依赖
    exclude: [
      'string.prototype.codepointat',
      'tiny-inflate'
    ]
  },

  // 定义全局变量
  define: {
    __DEV__: true,
    __VERSION__: JSON.stringify(process.env.npm_package_version || '0.3.0')
  },

  // 环境变量
  envPrefix: 'VITE_',

  // 日志级别
  logLevel: 'info',

  // 清除控制台
  clearScreen: false
})
