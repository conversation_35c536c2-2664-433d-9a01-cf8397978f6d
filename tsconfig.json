{
  "compilerOptions": {
    "target": "ES2020",
    "useDefineForClassFields": true,
    "module": "ESNext",
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "skipLibCheck": true,

    /* Bundler mode */
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "isolatedModules": true,
    "moduleDetection": "force",
    "noEmit": true,

    /* Linting */
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true,

    "suppressImplicitAnyIndexErrors": true,
    "ignoreDeprecations":"5.0",
    "noImplicitAny": false,

    "declaration": true,
    "declarationDir": "./src"
  },
  "include": ["src", "index.ts", "example/tools.ts", "example/main.ts"]
}
