#!/usr/bin/env node

/**
 * 简单的静态文件服务器，用于演示 Vue OFD Viewer
 * 避免 Vite 配置复杂性，直接提供静态文件服务
 */

const http = require('http');
const fs = require('fs');
const path = require('path');
const url = require('url');

const PORT = 8080;
const HOST = 'localhost';

// MIME 类型映射
const mimeTypes = {
  '.html': 'text/html',
  '.js': 'text/javascript',
  '.css': 'text/css',
  '.json': 'application/json',
  '.png': 'image/png',
  '.jpg': 'image/jpg',
  '.gif': 'image/gif',
  '.svg': 'image/svg+xml',
  '.wav': 'audio/wav',
  '.mp4': 'video/mp4',
  '.woff': 'application/font-woff',
  '.ttf': 'application/font-ttf',
  '.eot': 'application/vnd.ms-fontobject',
  '.otf': 'application/font-otf',
  '.wasm': 'application/wasm',
  '.ofd': 'application/octet-stream'
};

function getContentType(filePath) {
  const ext = path.extname(filePath).toLowerCase();
  return mimeTypes[ext] || 'application/octet-stream';
}

function serveFile(res, filePath) {
  fs.readFile(filePath, (err, content) => {
    if (err) {
      if (err.code === 'ENOENT') {
        // 文件不存在，返回 404
        res.writeHead(404, { 'Content-Type': 'text/html' });
        res.end(`
          <!DOCTYPE html>
          <html>
          <head>
            <title>404 - 文件未找到</title>
            <style>
              body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
              h1 { color: #e74c3c; }
              a { color: #3498db; text-decoration: none; }
              a:hover { text-decoration: underline; }
            </style>
          </head>
          <body>
            <h1>404 - 文件未找到</h1>
            <p>请求的文件不存在: ${filePath}</p>
            <p><a href="/">返回首页</a></p>
            <p><a href="/example/vue-demo/index.html">Vue 基础演示</a></p>
            <p><a href="/example/vue-demo/vue-app.html">Vue 完整演示</a></p>
          </body>
          </html>
        `);
      } else {
        // 服务器错误
        res.writeHead(500);
        res.end('服务器内部错误');
      }
    } else {
      // 成功读取文件
      const contentType = getContentType(filePath);
      res.writeHead(200, { 
        'Content-Type': contentType,
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization'
      });
      res.end(content);
    }
  });
}

function serveDirectory(res, dirPath, requestPath) {
  fs.readdir(dirPath, (err, files) => {
    if (err) {
      res.writeHead(500);
      res.end('无法读取目录');
      return;
    }

    const fileList = files.map(file => {
      const filePath = path.join(dirPath, file);
      const stat = fs.statSync(filePath);
      const isDir = stat.isDirectory();
      const href = path.join(requestPath, file).replace(/\\/g, '/');
      
      return `
        <li>
          <a href="${href}${isDir ? '/' : ''}">${file}${isDir ? '/' : ''}</a>
          <span style="color: #666; margin-left: 10px;">
            ${isDir ? '[目录]' : `[${(stat.size / 1024).toFixed(1)} KB]`}
          </span>
        </li>
      `;
    }).join('');

    res.writeHead(200, { 'Content-Type': 'text/html' });
    res.end(`
      <!DOCTYPE html>
      <html>
      <head>
        <title>目录浏览 - ${requestPath}</title>
        <style>
          body { font-family: Arial, sans-serif; padding: 20px; }
          h1 { color: #333; }
          ul { list-style: none; padding: 0; }
          li { padding: 5px 0; }
          a { color: #3498db; text-decoration: none; }
          a:hover { text-decoration: underline; }
          .back { margin-bottom: 20px; }
        </style>
      </head>
      <body>
        <h1>目录浏览: ${requestPath}</h1>
        ${requestPath !== '/' ? '<div class="back"><a href="../">← 返回上级目录</a></div>' : ''}
        <ul>
          ${fileList}
        </ul>
      </body>
      </html>
    `);
  });
}

const server = http.createServer((req, res) => {
  const parsedUrl = url.parse(req.url);
  let pathname = parsedUrl.pathname;
  
  // 处理根路径
  if (pathname === '/') {
    pathname = '/dev-index.html';
  }
  
  // 构建文件路径
  const filePath = path.join(__dirname, pathname);
  
  // 检查文件是否存在
  fs.stat(filePath, (err, stat) => {
    if (err) {
      // 文件不存在
      serveFile(res, filePath);
    } else if (stat.isDirectory()) {
      // 是目录，尝试查找 index.html
      const indexPath = path.join(filePath, 'index.html');
      fs.stat(indexPath, (indexErr) => {
        if (indexErr) {
          // 没有 index.html，显示目录列表
          serveDirectory(res, filePath, pathname);
        } else {
          // 有 index.html，直接提供
          serveFile(res, indexPath);
        }
      });
    } else {
      // 是文件，直接提供
      serveFile(res, filePath);
    }
  });
});

server.listen(PORT, HOST, () => {
  console.log(`\n🚀 Vue OFD Viewer 演示服务器已启动!`);
  console.log(`\n📍 访问地址:`);
  console.log(`   本地访问: http://${HOST}:${PORT}`);
  console.log(`   网络访问: http://localhost:${PORT}`);
  console.log(`\n📋 可用的演示页面:`);
  console.log(`   🏠 开发首页: http://${HOST}:${PORT}/`);
  console.log(`   📄 基础演示: http://${HOST}:${PORT}/example/vue-demo/index.html`);
  console.log(`   ⚡ 完整演示: http://${HOST}:${PORT}/example/vue-demo/vue-app.html`);
  console.log(`   🧪 测试页面: http://${HOST}:${PORT}/example/vue-demo/test.html`);
  console.log(`   🔧 原生演示: http://${HOST}:${PORT}/index.html`);
  console.log(`\n💡 提示: 按 Ctrl+C 停止服务器`);
  console.log(`\n⚠️  注意: 这是一个简单的静态文件服务器，仅用于演示目的`);
});

// 优雅关闭
process.on('SIGINT', () => {
  console.log('\n\n👋 正在关闭服务器...');
  server.close(() => {
    console.log('✅ 服务器已关闭');
    process.exit(0);
  });
});

process.on('SIGTERM', () => {
  console.log('\n\n👋 正在关闭服务器...');
  server.close(() => {
    console.log('✅ 服务器已关闭');
    process.exit(0);
  });
});
