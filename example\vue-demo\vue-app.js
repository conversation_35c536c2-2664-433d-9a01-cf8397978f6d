// Vue 3 应用配置
const { createApp, ref, reactive, computed, onMounted, nextTick } = Vue

const App = {
  setup() {
    // 引用
    const ofdViewerRef = ref(null)
    const logContainer = ref(null)
    
    // 响应式数据
    const currentFile = ref(null)
    const isDocumentLoaded = ref(false)
    
    const viewerConfig = reactive({
      showToolbar: true,
      autoLoad: true,
      scale: 1
    })
    
    const documentStatus = reactive({
      fileName: '',
      totalPages: 0,
      currentPage: 0,
      scale: 1,
      loadingStatus: '未加载'
    })
    
    const logs = ref([])
    
    // 计算属性
    const canGoPrev = computed(() => {
      return isDocumentLoaded.value && documentStatus.currentPage > 1
    })
    
    const canGoNext = computed(() => {
      return isDocumentLoaded.value && documentStatus.currentPage < documentStatus.totalPages
    })
    
    // 日志方法
    const addLog = (message, level = 'info') => {
      const timestamp = new Date().toLocaleTimeString()
      logs.value.push({
        timestamp,
        message,
        level
      })
      
      // 自动滚动到底部
      nextTick(() => {
        if (logContainer.value) {
          logContainer.value.scrollTop = logContainer.value.scrollHeight
        }
      })
      
      // 限制日志数量
      if (logs.value.length > 100) {
        logs.value.shift()
      }
    }
    
    const clearLogs = () => {
      logs.value = []
      addLog('日志已清空', 'info')
    }
    
    // 文件处理方法
    const handleFileUpload = (event) => {
      const file = event.target.files[0]
      if (file) {
        if (!file.name.toLowerCase().endsWith('.ofd')) {
          addLog('错误：请选择 .ofd 格式的文件', 'error')
          return
        }
        
        documentStatus.fileName = file.name
        documentStatus.loadingStatus = '准备加载'
        currentFile.value = file
        
        addLog(`选择文件: ${file.name} (${(file.size / 1024 / 1024).toFixed(2)} MB)`, 'info')
      }
    }
    
    const loadSampleFile = async () => {
      try {
        documentStatus.loadingStatus = '加载示例文件'
        addLog('尝试加载示例文件...', 'info')
        
        // 模拟示例文件加载
        // 在实际应用中，这里应该加载真实的示例文件
        setTimeout(() => {
          addLog('示例文件功能需要提供实际的 OFD 文件', 'error')
          documentStatus.loadingStatus = '示例文件不可用'
        }, 1000)
        
      } catch (error) {
        addLog(`加载示例文件失败: ${error.message}`, 'error')
        documentStatus.loadingStatus = '加载失败'
      }
    }
    
    // OFD Viewer 事件处理
    const onDocumentLoaded = (document) => {
      isDocumentLoaded.value = true
      documentStatus.loadingStatus = '加载完成'
      
      if (ofdViewerRef.value) {
        documentStatus.totalPages = ofdViewerRef.value.getTotalPages()
        documentStatus.currentPage = ofdViewerRef.value.getCurrentPage()
        documentStatus.scale = ofdViewerRef.value.getScale()
      }
      
      addLog(`文档加载成功: ${documentStatus.totalPages} 页`, 'success')
    }
    
    const onDocumentError = (error) => {
      isDocumentLoaded.value = false
      documentStatus.loadingStatus = '加载失败'
      addLog(`文档加载失败: ${error.message}`, 'error')
    }
    
    const onPageChange = (page) => {
      documentStatus.currentPage = page
      addLog(`页面切换到: ${page}`, 'info')
    }
    
    const onScaleChange = (scale) => {
      documentStatus.scale = scale
      viewerConfig.scale = scale
      addLog(`缩放变更为: ${Math.round(scale * 100)}%`, 'info')
    }
    
    const onSignatureClick = (data) => {
      addLog(`检测到签名点击事件`, 'info')
      console.log('签名数据:', data)
    }
    
    // 控制方法
    const zoomIn = () => {
      if (ofdViewerRef.value) {
        ofdViewerRef.value.zoomIn()
        addLog('执行放大操作', 'info')
      }
    }
    
    const zoomOut = () => {
      if (ofdViewerRef.value) {
        ofdViewerRef.value.zoomOut()
        addLog('执行缩小操作', 'info')
      }
    }
    
    const resetZoom = () => {
      if (ofdViewerRef.value) {
        ofdViewerRef.value.resetZoom()
        addLog('重置缩放比例', 'info')
      }
    }
    
    const goToFirstPage = () => {
      if (ofdViewerRef.value) {
        ofdViewerRef.value.firstPage()
        addLog('跳转到首页', 'info')
      }
    }
    
    const goToPrevPage = () => {
      if (ofdViewerRef.value) {
        ofdViewerRef.value.prevPage()
        addLog('跳转到上一页', 'info')
      }
    }
    
    const goToNextPage = () => {
      if (ofdViewerRef.value) {
        ofdViewerRef.value.nextPage()
        addLog('跳转到下一页', 'info')
      }
    }
    
    const goToLastPage = () => {
      if (ofdViewerRef.value) {
        ofdViewerRef.value.lastPage()
        addLog('跳转到末页', 'info')
      }
    }
    
    const getDocumentContent = () => {
      if (ofdViewerRef.value) {
        const content = ofdViewerRef.value.getContent()
        addLog(`获取文档内容: ${content.length} 字符`, 'success')
        console.log('文档内容:', content)
      }
    }
    
    // 生命周期
    onMounted(() => {
      addLog('OFD Viewer Vue 演示应用已启动', 'success')
    })
    
    return {
      // 引用
      ofdViewerRef,
      logContainer,
      
      // 数据
      currentFile,
      isDocumentLoaded,
      viewerConfig,
      documentStatus,
      logs,
      
      // 计算属性
      canGoPrev,
      canGoNext,
      
      // 方法
      handleFileUpload,
      loadSampleFile,
      onDocumentLoaded,
      onDocumentError,
      onPageChange,
      onScaleChange,
      onSignatureClick,
      zoomIn,
      zoomOut,
      resetZoom,
      goToFirstPage,
      goToPrevPage,
      goToNextPage,
      goToLastPage,
      getDocumentContent,
      clearLogs
    }
  },
  
  components: {
    'ofd-viewer': {
      template: `
        <div class="ofd-viewer">
          <!-- 工具栏 -->
          <div v-if="showToolbar" class="ofd-toolbar">
            <div class="toolbar-group">
              <input 
                ref="fileInput" 
                type="file" 
                accept=".ofd" 
                @change="handleFileChange" 
                style="display: none"
              />
              <button @click="selectFile" class="toolbar-btn">选择文件</button>
              <span v-if="fileName" class="file-name">{{ fileName }}</span>
            </div>
            
            <div class="toolbar-group">
              <button @click="zoomOut" :disabled="!isLoaded" class="toolbar-btn">缩小</button>
              <button @click="zoomIn" :disabled="!isLoaded" class="toolbar-btn">放大</button>
              <button @click="resetZoom" :disabled="!isLoaded" class="toolbar-btn">重置</button>
            </div>
            
            <div class="toolbar-group">
              <button @click="firstPage" :disabled="!canGoPrev" class="toolbar-btn">首页</button>
              <button @click="prevPage" :disabled="!canGoPrev" class="toolbar-btn">上一页</button>
              <span class="page-info">{{ currentPage }} / {{ totalPages }}</span>
              <button @click="nextPage" :disabled="!canGoNext" class="toolbar-btn">下一页</button>
              <button @click="lastPage" :disabled="!canGoNext" class="toolbar-btn">末页</button>
            </div>
          </div>
          
          <!-- 内容区域 -->
          <div class="ofd-content" ref="contentContainer">
            <div v-if="loading" class="loading">加载中...</div>
            <div v-else-if="error" class="error">{{ error }}</div>
            <div v-else-if="!src" class="loading">
              <h3>OFD Viewer 组件</h3>
              <p>请选择 OFD 文件进行预览</p>
              <div style="margin-top: 20px; text-align: left; max-width: 300px;">
                <h4>支持的功能：</h4>
                <ul style="list-style: none; padding: 0;">
                  <li>✅ OFD 文档解析和渲染</li>
                  <li>✅ 页面导航控制</li>
                  <li>✅ 缩放功能</li>
                  <li>✅ 工具栏集成</li>
                  <li>✅ 事件回调</li>
                  <li>✅ 响应式设计</li>
                </ul>
              </div>
            </div>
            <div v-else class="loading">
              <h3>模拟 OFD 文档显示</h3>
              <p>文件: {{ fileName }}</p>
              <p>页面: {{ currentPage }} / {{ totalPages }}</p>
              <p>缩放: {{ Math.round(currentScale * 100) }}%</p>
              <p style="margin-top: 20px; color: #666;">
                这是一个演示组件。在实际使用中，这里会显示真实的 OFD 文档内容。
              </p>
            </div>
          </div>
        </div>
      `,
      props: {
        src: [String, File, ArrayBuffer],
        showToolbar: { type: Boolean, default: true },
        autoLoad: { type: Boolean, default: true },
        scale: { type: Number, default: 1 }
      },
      emits: ['loaded', 'error', 'page-change', 'scale-change', 'signature-click'],
      data() {
        return {
          loading: false,
          error: '',
          fileName: '',
          currentPage: 1,
          totalPages: 5,
          currentScale: 1,
          isLoaded: false
        }
      },
      computed: {
        canGoPrev() {
          return this.isLoaded && this.currentPage > 1
        },
        canGoNext() {
          return this.isLoaded && this.currentPage < this.totalPages
        }
      },
      watch: {
        src: {
          handler(newSrc) {
            if (newSrc && this.autoLoad) {
              this.loadFile(newSrc)
            }
          },
          immediate: true
        },
        scale(newScale) {
          if (newScale !== this.currentScale) {
            this.currentScale = newScale
          }
        }
      },
      methods: {
        loadFile(file) {
          this.loading = true
          this.error = ''
          
          // 模拟加载过程
          setTimeout(() => {
            try {
              if (file instanceof File) {
                this.fileName = file.name
              } else {
                this.fileName = 'document.ofd'
              }
              
              this.isLoaded = true
              this.loading = false
              this.currentPage = 1
              this.totalPages = Math.floor(Math.random() * 10) + 1
              
              this.$emit('loaded', { 
                pages: this.totalPages,
                fileName: this.fileName 
              })
            } catch (err) {
              this.error = '加载失败: ' + err.message
              this.loading = false
              this.$emit('error', err)
            }
          }, 1500)
        },
        
        selectFile() {
          this.$refs.fileInput?.click()
        },
        
        handleFileChange(event) {
          const file = event.target.files[0]
          if (file) {
            this.loadFile(file)
          }
        },
        
        // 导航方法
        firstPage() {
          this.currentPage = 1
          this.$emit('page-change', this.currentPage)
        },
        
        prevPage() {
          if (this.currentPage > 1) {
            this.currentPage--
            this.$emit('page-change', this.currentPage)
          }
        },
        
        nextPage() {
          if (this.currentPage < this.totalPages) {
            this.currentPage++
            this.$emit('page-change', this.currentPage)
          }
        },
        
        lastPage() {
          this.currentPage = this.totalPages
          this.$emit('page-change', this.currentPage)
        },
        
        // 缩放方法
        zoomIn() {
          this.currentScale = Math.min(this.currentScale * 1.2, 3)
          this.$emit('scale-change', this.currentScale)
        },
        
        zoomOut() {
          this.currentScale = Math.max(this.currentScale * 0.8, 0.5)
          this.$emit('scale-change', this.currentScale)
        },
        
        resetZoom() {
          this.currentScale = 1
          this.$emit('scale-change', this.currentScale)
        },
        
        // 公开方法
        getCurrentPage() { return this.currentPage },
        getTotalPages() { return this.totalPages },
        getScale() { return this.currentScale },
        getContent() { return `模拟文档内容 - 第${this.currentPage}页` }
      }
    }
  }
}

// 创建并挂载应用
createApp(App).mount('#app')
