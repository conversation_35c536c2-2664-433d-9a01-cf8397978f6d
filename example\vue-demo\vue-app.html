<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OFD Viewer Vue 完整演示</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
        }
        
        .app-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .app-header {
            text-align: center;
            margin-bottom: 30px;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .app-title {
            font-size: 2.5rem;
            color: #333;
            margin-bottom: 10px;
        }
        
        .app-subtitle {
            font-size: 1.2rem;
            color: #666;
        }
        
        .demo-grid {
            display: grid;
            grid-template-columns: 1fr 2fr;
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .control-panel {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px;
            height: fit-content;
        }
        
        .panel-title {
            font-size: 1.3rem;
            color: #333;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #4a90e2;
        }
        
        .control-group {
            margin-bottom: 20px;
        }
        
        .control-label {
            display: block;
            font-weight: 500;
            color: #333;
            margin-bottom: 8px;
        }
        
        .control-input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .btn {
            padding: 10px 20px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background: white;
            cursor: pointer;
            transition: all 0.2s;
            font-size: 14px;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        
        .btn:hover {
            background: #f0f0f0;
            border-color: #4a90e2;
        }
        
        .btn-primary {
            background: #4a90e2;
            color: white;
            border-color: #4a90e2;
        }
        
        .btn-primary:hover {
            background: #357abd;
        }
        
        .btn-success {
            background: #28a745;
            color: white;
            border-color: #28a745;
        }
        
        .btn-success:hover {
            background: #218838;
        }
        
        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        
        .viewer-panel {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .viewer-header {
            background: #4a90e2;
            color: white;
            padding: 15px 20px;
            font-size: 1.2rem;
            font-weight: 600;
        }
        
        .viewer-container {
            height: 600px;
            position: relative;
        }
        
        .status-panel {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px;
        }
        
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        
        .status-item {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            border-left: 4px solid #4a90e2;
        }
        
        .status-label {
            font-size: 12px;
            color: #666;
            text-transform: uppercase;
            margin-bottom: 5px;
        }
        
        .status-value {
            font-size: 18px;
            font-weight: 600;
            color: #333;
        }
        
        .log-panel {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px;
            margin-top: 20px;
        }
        
        .log-content {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            height: 200px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            line-height: 1.4;
        }
        
        .log-entry {
            margin-bottom: 5px;
            padding: 2px 0;
        }
        
        .log-timestamp {
            color: #666;
        }
        
        .log-level-info {
            color: #007bff;
        }
        
        .log-level-error {
            color: #dc3545;
        }
        
        .log-level-success {
            color: #28a745;
        }
        
        .error-message {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
            border-radius: 4px;
            padding: 12px;
            margin-bottom: 15px;
        }
        
        .success-message {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
            border-radius: 4px;
            padding: 12px;
            margin-bottom: 15px;
        }
        
        @media (max-width: 1024px) {
            .demo-grid {
                grid-template-columns: 1fr;
            }
            
            .status-grid {
                grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            }
        }
        
        @media (max-width: 768px) {
            .app-container {
                padding: 10px;
            }
            
            .app-title {
                font-size: 2rem;
            }
            
            .viewer-container {
                height: 400px;
            }
        }
        
        /* OFD Viewer 样式 */
        .ofd-viewer {
            height: 100%;
            display: flex;
            flex-direction: column;
        }
        
        .ofd-toolbar {
            background: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
            padding: 10px 15px;
            display: flex;
            align-items: center;
            gap: 15px;
            flex-wrap: wrap;
        }
        
        .toolbar-group {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .toolbar-btn {
            padding: 6px 12px;
            border: 1px solid #ced4da;
            border-radius: 3px;
            background: white;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.2s;
        }
        
        .toolbar-btn:hover:not(:disabled) {
            background: #e9ecef;
            border-color: #adb5bd;
        }
        
        .toolbar-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        
        .page-info {
            font-size: 12px;
            color: #495057;
            font-weight: 500;
        }
        
        .file-name {
            font-size: 12px;
            color: #6c757d;
            max-width: 200px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        
        .ofd-content {
            flex: 1;
            overflow: auto;
            background: #e9ecef;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .loading, .error {
            text-align: center;
            padding: 40px;
            color: #6c757d;
        }
        
        .error {
            color: #dc3545;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="app-container">
            <!-- 应用头部 -->
            <div class="app-header">
                <h1 class="app-title">OFD Viewer Vue 完整演示</h1>
                <p class="app-subtitle">基于 liteofd 的专业 OFD 文档查看器组件</p>
            </div>
            
            <!-- 主要演示区域 -->
            <div class="demo-grid">
                <!-- 控制面板 -->
                <div class="control-panel">
                    <h3 class="panel-title">控制面板</h3>
                    
                    <!-- 文件上传 -->
                    <div class="control-group">
                        <label class="control-label">选择 OFD 文件</label>
                        <input 
                            type="file" 
                            accept=".ofd" 
                            @change="handleFileUpload"
                            class="control-input"
                        />
                    </div>
                    
                    <!-- 示例文件 -->
                    <div class="control-group">
                        <label class="control-label">示例文件</label>
                        <button @click="loadSampleFile" class="btn btn-primary">
                            加载示例文件
                        </button>
                    </div>
                    
                    <!-- 查看器配置 -->
                    <div class="control-group">
                        <label class="control-label">查看器配置</label>
                        <label>
                            <input 
                                type="checkbox" 
                                v-model="viewerConfig.showToolbar"
                            /> 显示工具栏
                        </label>
                        <br>
                        <label>
                            <input 
                                type="checkbox" 
                                v-model="viewerConfig.autoLoad"
                            /> 自动加载
                        </label>
                    </div>
                    
                    <!-- 缩放控制 -->
                    <div class="control-group">
                        <label class="control-label">缩放控制</label>
                        <button @click="zoomIn" :disabled="!isDocumentLoaded" class="btn">
                            放大 (+)
                        </button>
                        <button @click="zoomOut" :disabled="!isDocumentLoaded" class="btn">
                            缩小 (-)
                        </button>
                        <button @click="resetZoom" :disabled="!isDocumentLoaded" class="btn">
                            重置 (100%)
                        </button>
                    </div>
                    
                    <!-- 页面导航 -->
                    <div class="control-group">
                        <label class="control-label">页面导航</label>
                        <button @click="goToFirstPage" :disabled="!canGoPrev" class="btn">
                            首页
                        </button>
                        <button @click="goToPrevPage" :disabled="!canGoPrev" class="btn">
                            上一页
                        </button>
                        <button @click="goToNextPage" :disabled="!canGoNext" class="btn">
                            下一页
                        </button>
                        <button @click="goToLastPage" :disabled="!canGoNext" class="btn">
                            末页
                        </button>
                    </div>
                    
                    <!-- 其他功能 -->
                    <div class="control-group">
                        <label class="control-label">其他功能</label>
                        <button @click="getDocumentContent" :disabled="!isDocumentLoaded" class="btn btn-success">
                            获取文档内容
                        </button>
                        <button @click="clearLogs" class="btn">
                            清空日志
                        </button>
                    </div>
                </div>
                
                <!-- 查看器面板 -->
                <div class="viewer-panel">
                    <div class="viewer-header">
                        OFD 文档查看器
                    </div>
                    <div class="viewer-container">
                        <ofd-viewer
                            ref="ofdViewerRef"
                            :src="currentFile"
                            :show-toolbar="viewerConfig.showToolbar"
                            :auto-load="viewerConfig.autoLoad"
                            :scale="viewerConfig.scale"
                            @loaded="onDocumentLoaded"
                            @error="onDocumentError"
                            @page-change="onPageChange"
                            @scale-change="onScaleChange"
                            @signature-click="onSignatureClick"
                        />
                    </div>
                </div>
            </div>
            
            <!-- 状态面板 -->
            <div class="status-panel">
                <h3 class="panel-title">状态信息</h3>
                <div class="status-grid">
                    <div class="status-item">
                        <div class="status-label">文件名</div>
                        <div class="status-value">{{ documentStatus.fileName || '未选择' }}</div>
                    </div>
                    <div class="status-item">
                        <div class="status-label">总页数</div>
                        <div class="status-value">{{ documentStatus.totalPages || 0 }}</div>
                    </div>
                    <div class="status-item">
                        <div class="status-label">当前页</div>
                        <div class="status-value">{{ documentStatus.currentPage || 0 }}</div>
                    </div>
                    <div class="status-item">
                        <div class="status-label">缩放比例</div>
                        <div class="status-value">{{ Math.round((documentStatus.scale || 1) * 100) }}%</div>
                    </div>
                    <div class="status-item">
                        <div class="status-label">加载状态</div>
                        <div class="status-value">{{ documentStatus.loadingStatus }}</div>
                    </div>
                </div>
            </div>
            
            <!-- 日志面板 -->
            <div class="log-panel">
                <h3 class="panel-title">事件日志</h3>
                <div class="log-content" ref="logContainer">
                    <div 
                        v-for="(log, index) in logs" 
                        :key="index" 
                        class="log-entry"
                        :class="`log-level-${log.level}`"
                    >
                        <span class="log-timestamp">[{{ log.timestamp }}]</span>
                        <span class="log-message">{{ log.message }}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Vue 3 CDN -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    
    <!-- 主应用脚本 -->
    <script src="./vue-app.js"></script>
</body>
</html>
